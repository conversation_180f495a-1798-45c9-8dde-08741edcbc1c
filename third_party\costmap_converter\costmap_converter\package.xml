<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>costmap_converter</name>
  <version>0.1.2</version>
  <description>
    A ros package that includes plugins and nodes to convert occupied costmap2d cells to primitive types.
  </description>

  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>">Otniel <PERSON></author> 

  <license>BSD</license>

  <url type="website">http://wiki.ros.org/costmap_converter</url>
  
  <buildtool_depend>ament_cmake</buildtool_depend>
    
  <depend>class_loader</depend>
  <depend>costmap_converter_msgs</depend>
  <depend>cv_bridge</depend>
  <depend>geometry_msgs</depend>
  <depend>nav2_costmap_2d</depend>
  <depend>tf2</depend>
  <depend>pluginlib</depend>
  <depend>rclcpp</depend>
  <!-- <depend>dynamic_reconfigure</depend> -->

  <test_depend>ament_cmake_gtest</test_depend>
  
  <!-- The export tag contains other, unspecified, tags -->
  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
