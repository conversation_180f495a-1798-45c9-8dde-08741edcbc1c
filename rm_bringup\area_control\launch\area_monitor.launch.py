from launch import LaunchDescription
from launch_ros.actions import Node
from launch.substitutions import LaunchConfiguration
from launch.actions import DeclareLaunchArgument

def generate_launch_description():
    # 声明启动参数
    use_sim_time = LaunchConfiguration('use_sim_time', default='false')
    
    # 区域配置
    areas = [
        "{1:1.45,-5.98},{2:6.08,-5.91},{3:6.08,-7.31},{4:2.78,-7.16},{5:1.07,-9.24}",  # 区域1
        "{1:17.28,3.33},{2:20.65,3.37},{3:22.29,4.98},{4:22.16,1.94},{5:17.22,2.0}"	#区域2
    ]
    
    # 创建区域监控节点
    area_monitor_node = Node(
        package='area_control',
        executable='area_monitor',
        name='area_monitor',
        output='screen',
        parameters=[{
            'use_sim_time': use_sim_time,
            'areas': areas
        }]
    )
    
    return LaunchDescription([
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='false',
            description='Use simulation (Gazebo) clock if true'
        ),
        area_monitor_node
    ])
