<library path="costmap_converter">
  
  <class type="costmap_converter::CostmapToPolygonsDBSMCCH" base_class_type="costmap_converter::BaseCostmapToPolygons">
  <description>
      This class converts costmap2d obstacles into a vector of convex polygons. 
      Clustering is performed with DBScan. Convex polygons are detected using the Monotone Chain Convex Hull algorithm.
  </description>
  </class>
  
  <class type="costmap_converter::CostmapToLinesDBSMCCH" base_class_type="costmap_converter::BaseCostmapToPolygons">
  <description>
      This class converts costmap2d obstacles into a vector of lines (represented as polygon msg). 
      Clustering is performed with DBScan. Clusters are transformed into convex polygons using the Monotone Chain Convex Hull algorithm.
      The resulting keypoints are used for possible line candidates.
      A line is only defined if there exist a specified number of support points between each keypoint pair.
  </description>
  </class>
  
  <class type="costmap_converter::CostmapToLinesDBSRANSAC" base_class_type="costmap_converter::BaseCostmapToPolygons">
  <description>
      This class converts costmap2d obstacles into a vector of lines (represented as polygon msg). 
      Clustering is performed with DBScan. For each cluster RANSAC is applied sequentally to fit multiple line models.
  </description>
  </class>
  
  <class type="costmap_converter::CostmapToPolygonsDBSConcaveHull" base_class_type="costmap_converter::BaseCostmapToPolygons">
  <description>
      This class converts costmap2d obstacles into a vector of non-convex (concave) polygons. 
  </description>
  </class>

  <class type="costmap_converter::CostmapToDynamicObstacles" base_class_type="costmap_converter::BaseCostmapToPolygons">
  <description>
      This class detects and tracks obstacles from a sequence of costmaps. 
  </description>
  </class>
  
</library>
