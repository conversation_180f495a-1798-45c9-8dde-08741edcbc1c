#!/usr/bin/env python

from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

# For integers and doubles:
#       Name                    Type      Reconfiguration level
#       Description
#       Default  Min  Max


gen.add("cluster_max_distance", double_t, 0, 
	"Parameter for DB_Scan, maximum distance to neighbors [m]",
	0.4, 0.0, 10.0)

gen.add("cluster_min_pts", int_t, 0, 
	"Parameter for DB_Scan: minimum number of points that define a cluster",
	2, 1, 20)

gen.add("cluster_max_pts", int_t, 0, 
  "Parameter for DB_Scan: maximum number of points that define a cluster (limit cluster size to avoid large L- and U-shapes)",
  30, 2, 200)

gen.add("ransac_inlier_distance", double_t, 0, 
	"Maximum distance to the line segment for inliers",
	0.2, 0.0, 10.0)

gen.add("ransac_min_inliers", int_t, 0, 
	"Minimum numer of inliers required to form a line",
	10, 0, 100)

gen.add("ransac_no_iterations", int_t, 0, 
	"Number of ransac iterations",
	2000, 1, 10000)

gen.add("ransac_remainig_outliers", int_t, 0, 
	"Repeat ransac until the number of outliers is as specified here",
	3, 0, 50)

gen.add("ransac_convert_outlier_pts",   bool_t,   0, 
	"Convert remaining outliers to single points.",
	True)

gen.add("ransac_filter_remaining_outlier_pts",   bool_t,   0, 
	"Filter the interior of remaining outliers and keep only keypoints of their convex hull",
	False)

gen.add("convex_hull_min_pt_separation",   double_t,   0,
	"Clear keypoints of the convex polygon that are close to each other [distance in meters] (0: keep all)",
	0.1, 0.0, 10.0)	


exit(gen.generate("costmap_converter", "standalone_converter", "CostmapToLinesDBSRANSAC"))
