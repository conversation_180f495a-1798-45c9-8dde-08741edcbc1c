<?xml version="1.0"?>
<package format="2">
  <name>teb_local_planner</name>

  <version>0.9.1</version>
  
  <description>
    The teb_local_planner package implements a plugin
    to the base_local_planner of the 2D navigation stack.
    The underlying method called Timed Elastic Band locally optimizes
    the robot's trajectory with respect to trajectory execution time,
    separation from obstacles and compliance with kinodynamic constraints at runtime.	
  </description>
  
  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>BSD</license>

  <url type="website">http://wiki.ros.org/teb_local_planner</url>

  <author email="<EMAIL>"><PERSON></author>
  
  <buildtool_depend>ament_cmake</buildtool_depend>
  
  <depend>costmap_converter</depend>
  <depend>costmap_converter_msgs</depend>

  <depend>geometry_msgs</depend>
  <depend>libg2o</depend>
  <depend>dwb_critics</depend>
  <depend>nav2_core</depend>
  <depend>nav2_costmap_2d</depend>
  <depend>nav2_msgs</depend>
  <depend>nav2_util</depend>
  <depend>pluginlib</depend>
  <depend>rclcpp</depend>
  <depend>rclcpp_action</depend>
  <depend>rclcpp_lifecycle</depend>
  <depend>std_msgs</depend>
  <depend>teb_msgs</depend>
  <depend>tf2</depend>
  <depend>tf2_eigen</depend>
  <depend>visualization_msgs</depend>
  <depend>builtin_interfaces</depend>
  <exec_depend>nav2_bringup</exec_depend>
  
  <test_depend>ament_cmake_gtest</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
    <nav2_core plugin="${prefix}/teb_local_planner_plugin.xml" />
  </export>
</package>
