cmake_minimum_required(VERSION 3.5)
project(pointcloud_to_laserscan)

# Use C++14
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 14)
endif()
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fexceptions")

# Compiler settings for performance and threading
add_definitions(-DROOT_DIR="${CMAKE_CURRENT_SOURCE_DIR}/")
add_compile_options(-O3 -pthread -fexceptions)

# Processor count and definitions
message(STATUS "Current CPU architecture: ${CMAKE_SYSTEM_PROCESSOR}")
if(CMAKE_SYSTEM_PROCESSOR MATCHES "(x86)|(X86)|(amd64)|(AMD64)")
  include(ProcessorCount)
  ProcessorCount(N)
  message(STATUS "Processor number:  ${N}")
  if(N GREATER 5)
    add_definitions(-DMP_EN)
    add_definitions(-DMP_PROC_NUM=4)
    message(STATUS "core for MP:  3")
  elseif(N GREATER 3)
    math(EXPR PROC_NUM "${N} - 2")
    add_definitions(-DMP_EN)
    add_definitions(-DMP_PROC_NUM="${PROC_NUM}")
    message(STATUS "core for MP:  ${PROC_NUM}")
  else()
    add_definitions(-DMP_PROC_NUM=1)
  endif()
else()
  add_definitions(-DMP_PROC_NUM=1)
endif()

# Find packages
find_package(ament_cmake REQUIRED)
find_package(PCL REQUIRED)
find_package(laser_geometry REQUIRED)
find_package(message_filters REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclcpp_components REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(tf2 REQUIRED)
find_package(tf2_ros REQUIRED)
find_package(tf2_sensor_msgs REQUIRED)
find_package(pcl_ros REQUIRED)
find_package(pcl_conversions REQUIRED)

# Include directories
include_directories(
  include
  ${PCL_INCLUDE_DIRS}
)

# Declare libraries
add_library(laserscan_to_pointcloud SHARED src/laserscan_to_pointcloud_node.cpp)
target_compile_definitions(laserscan_to_pointcloud PRIVATE "POINTCLOUD_TO_LASERSCAN_BUILDING_DLL")
target_include_directories(laserscan_to_pointcloud PUBLIC
  "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>"
  "$<INSTALL_INTERFACE:include/${PROJECT_NAME}>"
)
target_link_libraries(laserscan_to_pointcloud
  laser_geometry::laser_geometry
  message_filters::message_filters
  rclcpp::rclcpp
  rclcpp_components::component
  tf2::tf2
  tf2_ros::tf2_ros
)
rclcpp_components_register_node(laserscan_to_pointcloud
  PLUGIN "pointcloud_to_laserscan::LaserScanToPointCloudNode"
  EXECUTABLE laserscan_to_pointcloud_node)

add_library(pointcloud_to_laserscan SHARED src/pointcloud_to_laserscan_node.cpp)
target_compile_definitions(pointcloud_to_laserscan PRIVATE "POINTCLOUD_TO_LASERSCAN_BUILDING_DLL")
target_include_directories(pointcloud_to_laserscan PUBLIC
  "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>"
  "$<INSTALL_INTERFACE:include/${PROJECT_NAME}>"
)
target_link_libraries(pointcloud_to_laserscan
  laser_geometry::laser_geometry
  message_filters::message_filters
  rclcpp::rclcpp
  rclcpp_components::component
  tf2::tf2
  tf2_ros::tf2_ros
  ${PCL_LIBRARIES}
)
ament_target_dependencies(pointcloud_to_laserscan
  laser_geometry
  message_filters
  rclcpp
  rclcpp_components
  sensor_msgs
  tf2
  tf2_ros
  tf2_sensor_msgs
  pcl_ros
  pcl_conversions
)
rclcpp_components_register_node(pointcloud_to_laserscan
  PLUGIN "pointcloud_to_laserscan::PointCloudToLaserScanNode"
  EXECUTABLE pointcloud_to_laserscan_node)

# Declare executables
add_executable(dummy_pointcloud_publisher src/dummy_pointcloud_publisher.cpp)
target_link_libraries(dummy_pointcloud_publisher
  rclcpp::rclcpp
  "${sensor_msgs_TARGETS}"
)

# Install targets
install(TARGETS
  laserscan_to_pointcloud
  pointcloud_to_laserscan
  EXPORT export_${PROJECT_NAME}
  RUNTIME DESTINATION bin
  LIBRARY DESTINATION lib
  ARCHIVE DESTINATION lib)

install(TARGETS
  dummy_pointcloud_publisher
  DESTINATION lib/${PROJECT_NAME}
)

install(DIRECTORY include
  DESTINATION include/${PROJECT_NAME}
)

install(DIRECTORY launch
  DESTINATION share/${PROJECT_NAME}
)

# Export dependencies
ament_export_dependencies(
  laser_geometry
  message_filters
  rclcpp
  rclcpp_components
  sensor_msgs
  tf2
  tf2_ros
)

ament_package()