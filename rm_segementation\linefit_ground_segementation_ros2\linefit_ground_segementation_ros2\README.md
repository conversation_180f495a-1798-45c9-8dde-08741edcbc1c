# linefit_ground_segmentation

Implementation of the ground segmentation algorithm proposed in 
```
@inproceedings{<PERSON><PERSON><PERSON>bach2010fast,
  title={Fast segmentation of 3d point clouds for ground vehicles},
  author={<PERSON><PERSON><PERSON><PERSON>, Michael and <PERSON>, Felix <PERSON> and <PERSON>, H-J},
  booktitle={Intelligent Vehicles Symposium (IV), 2010 IEEE},
  pages={560--565},
  year={2010},
  organization={IEEE}
}
```
The `linefit_ground_segmentation` package contains the ground segmentation library.
A ROS interface is available in `linefit_ground_segmentation_ros` 

The library can be compiled separately from the ROS interface if you're not using ROS.

## Installation

```bash
colcon build --symlink-install
```

## Launch instructions

The ground segmentation ROS node can be launch by executing `ros2 launch linefit_ground_segmentation_ros segmentation.launch`.
Input and output topic names can be specified in the same file.

Getting up and running with your own point cloud source should be as simple as:

1. Change the `input_topic` parameter in `segmentation.launch` to your topic.
2. Adjust the `sensor_height` parameter in `segmentation_params.yaml` to the height where the sensor is mounted on your robot (e.g. KITTI Velodyne: 1.8m)

## Parameter description

Parameters are set in `linefit_ground_segmentation_ros/launch/segmentation_params.yaml`

该算法的工作假设是您知道传感器离地高度。因此，您必须根据机器人的规格调整sensor_height，否则，它将无法工作。

默认参数应适用于KITTI数据集。 

### Ground Condition
- **sensor_height**  Sensor height above ground.
- **max_dist_to_line**  视为地面的点到线的最大垂直距离。
- **max_slope**  直线的最大坡度
- **min_slope**  Minimum slope of a line.
- **max_fit_error**  Maximum error a point is allowed to have in a line fit.
- **max_start_height**  Maximum height difference between new point and estimated ground height to start a new line.
- **long_threshold**  Distance after which the max_height condition is applied.
- **max_height**  Maximum height difference between line points when they are farther apart than *long_threshold*.
- **line_search_angle**  How far to search in angular direction to find a line. A higher angle helps fill "holes" in the ground segmentation.
- **gravity_aligned_frame**  Name of a coordinate frame which has its z-axis aligned with gravity. If specified, the incoming point cloud will be rotated, but not translated into this coordinate frame. If left empty, the sensor frame will be used.

### Segmentation

- **r_min**  Distance at which segmentation starts.
- **r_max**  Distance at which segmentation ends.
- **n_bins**  Number of radial bins.
- **n_segments**  Number of angular segments.

### Other

- **n_threads**  Number of threads to use.
- **latch**  Latch output point clouds in ROS node. 
- **visualize** Visualize the segmentation result. **ONLY FOR DEBUGGING.** Do not set true during online operation.
