<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4"
      main_tree_to_execute="BehaviorTree">
  <BehaviorTree ID="BehaviorTree">
    <ReactiveSequence>
      <SubTree ID="获取比赛相关信息"
               _autoremap="true"/>
      <WhileDoElse>
        <ISGameStart message="{game_progress_status}"/>
        <WhileDoElse>
          <IfRestoresHpOrPill both_camp_hp="{both_camp_hp}"
                              sentry_blood_threshold="170"
                              sentry_full_blood="400"
                              game_time="{game_time}"
                              projectile_number="{permitted_pill_number}"/>
          <SubTree ID="路线(回到补给区)"
                   _autoremap="true"/>
          <WhileDoElse>
            <IsTimeBack game_time="{game_time}"
                        set_minutes="5"/>
            <SubTree ID="比赛时间&gt;=设定时间，返回基地"
                     _autoremap="true"/>
            <SubTree ID="比赛时间&lt;设定时间，正常策略"
                     _autoremap="true"/>
          </WhileDoElse>
        </WhileDoElse>
        <Sleep msec="2000"/>
      </WhileDoElse>
    </ReactiveSequence>
  </BehaviorTree>

  <BehaviorTree ID="巡逻区(中央高地)">
    <SequenceWithMemory>
      <SendGoal name="中央高地巡逻点1"
                goal_pose="8.54;-1.93;0; 0;0;0;1"
                action_name="navigate_to_pose"/>
      <Sleep msec="5000"/>
      <SendGoal name="中央高地巡逻点2"
                goal_pose="8.10;-8.74;0; 0;0;0;1"
                action_name="navigate_to_pose"/>
      <Sleep msec="5000"/>
      <SendGoal name="中央高地巡逻点3"
                goal_pose="14.81;-1.96;0; 0;0;0;1"
                action_name="navigate_to_pose"/>
      <Sleep msec="5000"/>
      <SendGoal name="中央高地巡逻点4"
                goal_pose="10.82;0.44;0; 0;0;0;1"
                action_name="navigate_to_pose"/>
      <Sleep msec="5000"/>
    </SequenceWithMemory>
  </BehaviorTree>

  <BehaviorTree ID="巡逻区(基地)">
    <SequenceWithMemory>
      <SendGoal name="基地巡逻点1"
                goal_pose="5.38;-4.40;0; 0;0;0;1"
                action_name="navigate_to_pose"/>
      <Sleep msec="5000"/>
      <SendGoal name="基地巡逻点2"
                goal_pose="5.44;-0.41;0; 0;0;0;1"
                action_name="navigate_to_pose"/>
      <Sleep msec="5000"/>
    </SequenceWithMemory>
  </BehaviorTree>

  <BehaviorTree ID="巡逻区(敌方前哨战)">
    <SequenceWithMemory>
      <SendGoal name="敌方前哨战巡逻点1"
                goal_pose="11.35;0.53;0; 0;0;0;1"
                action_name="navigate_to_pose"/>
      <Sleep msec="2000"/>
    </SequenceWithMemory>
  </BehaviorTree>

  <BehaviorTree ID="比赛时间&lt;设定时间，正常策略">
    <WhileDoElse>
      <IsDetectEnemy message="{detect_enemy_status}"/>
      <SubTree ID="识别到敌人后停下来"
               _autoremap="true"/>
      <WhileDoElse>
        <IfEnemyOutpostDie enemy_outpost_status="{enemy_outpost_status}"/>
        <SubTree ID="巡逻区(中央高地)"
                 _autoremap="true"/>
        <SubTree ID="巡逻区(敌方前哨战)"
                 _autoremap="true"/>
      </WhileDoElse>
    </WhileDoElse>
  </BehaviorTree>

  <BehaviorTree ID="比赛时间&gt;=设定时间，返回基地">
    <WhileDoElse>
      <IsDetectEnemy message="{detect_enemy_status}"/>
      <SubTree ID="识别到敌人后停下来"
               _autoremap="true"/>
      <SubTree ID="巡逻区(基地)"
               _autoremap="true"/>
    </WhileDoElse>
  </BehaviorTree>

  <BehaviorTree ID="获取比赛相关信息">
    <Sequence>
      <SubGameStatus topic_name="game_progress_status"
                     game_status="{game_progress_status}"/>
      <SubProjectileNumber topic_name="permitted_pill_number"
                           projectile_number="{permitted_pill_number}"/>
      <SubBothCampHp topic_name="both_camp_hp"
                     both_camp_hp="{both_camp_hp}"/>
      <SubEnemyOutpostStatus topic_name="enemy_outpost_status"
                             enemy_outpost_status="{enemy_outpost_status}"/>
      <SubGameTime topic_name="game_time"
                   game_time="{game_time}"/>
      <SubIsDetectEnemy topic_name="control_"
                        detect_enemy_status="{detect_enemy_status}"/>
    </Sequence>
  </BehaviorTree>

  <BehaviorTree ID="识别到敌人后停下来">
    <Sequence>
      <RetryUntilSuccessful num_attempts="10000">
        <GetCurrentLocation current_location="{current_location}"/>
      </RetryUntilSuccessful>
      <Repeat num_cycles="10000">
        <RetryUntilSuccessful num_attempts="10000">
          <Sequence>
            <SendGoal goal_pose="{current_location}"
                      action_name="navigate_to_pose"/>
            <Sleep msec="2000"/>
          </Sequence>
        </RetryUntilSuccessful>
      </Repeat>
    </Sequence>
  </BehaviorTree>

  <BehaviorTree ID="路线(回到补给区)">
    <Sequence>
      <SendGoal name="补给区"
                goal_pose="0.32;-6.56;0; 0;0;0;1"
                action_name="navigate_to_pose"/>
      <Sleep msec="2000"/>
    </Sequence>
  </BehaviorTree>

  <!-- Description of Node Models (used by Groot) -->
  <TreeNodesModel>
    <Action ID="GetCurrentLocation"
            editable="true">
      <output_port name="current_location"/>
    </Action>
    <Condition ID="ISGameStart"
               editable="true">
      <input_port name="message"/>
    </Condition>
    <Condition ID="IfEnemyOutpostDie"
               editable="true">
      <input_port name="enemy_outpost_status"/>
    </Condition>
    <Condition ID="IfRestoresHpOrPill"
               editable="true">
      <input_port name="both_camp_hp"/>
      <input_port name="sentry_blood_threshold"/>
      <input_port name="sentry_full_blood"/>
      <input_port name="game_time"/>
      <input_port name="projectile_number"/>
    </Condition>
    <Condition ID="IsDetectEnemy"
               editable="true">
      <input_port name="message"/>
    </Condition>
    <Condition ID="IsTimeBack"
               editable="true">
      <input_port name="game_time"/>
      <input_port name="set_minutes"/>
    </Condition>
    <Action ID="SendGoal"
            editable="true">
      <input_port name="goal_pose"
                  default="0;0;0; 0;0;0;1"/>
      <input_port name="action_name"
                  default="navigate_to_pose"/>
    </Action>
    <Action ID="SubBothCampHp"
            editable="true">
      <input_port name="topic_name"/>
      <output_port name="both_camp_hp"/>
    </Action>
    <Action ID="SubEnemyOutpostStatus"
            editable="true">
      <input_port name="topic_name"/>
      <output_port name="enemy_outpost_status"/>
    </Action>
    <Action ID="SubGameStatus"
            editable="true">
      <input_port name="topic_name"/>
      <output_port name="game_status"/>
    </Action>
    <Action ID="SubGameTime"
            editable="true">
      <input_port name="topic_name"/>
      <output_port name="game_time"/>
    </Action>
    <Action ID="SubIsDetectEnemy"
            editable="true">
      <input_port name="topic_name"/>
      <output_port name="detect_enemy_status"/>
    </Action>
    <Action ID="SubProjectileNumber"
            editable="true">
      <input_port name="topic_name"/>
      <output_port name="projectile_number"/>
    </Action>
  </TreeNodesModel>

</root>
