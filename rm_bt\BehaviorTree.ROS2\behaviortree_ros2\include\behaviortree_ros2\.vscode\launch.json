{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": false, "cwd": "/home/<USER>/NUDT_bt/src/BehaviorTree.ROS2/behaviortree_ros2/include/behaviortree_ros2", "program": "/home/<USER>/NUDT_bt/src/BehaviorTree.ROS2/behaviortree_ros2/include/behaviortree_ros2/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}