<?xml version="1.0"?>
<?xml-model
  href="http://download.ros.org/schema/package_format3.xsd"
  schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>fake_vel_transform</name>
  <version>0.1.0</version>
  <description>A template for ROS packages.</description>
  <license>MIT</license>
  <author email="<EMAIL>"><PERSON><PERSON></author>
  <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>

  <!-- buildtool_depend: dependencies of the build process -->
  <buildtool_depend>ament_cmake</buildtool_depend>

  <!-- depend: build, export, and execution dependency -->
  <depend>rclcpp</depend>
  <depend>rclcpp_components</depend>
  <depend>geometry_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>tf2_ros</depend>
  <depend>tf2_geometry_msgs</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_cmake_clang_format</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
