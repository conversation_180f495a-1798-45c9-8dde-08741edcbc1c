{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": false, "cwd": "/home/<USER>/pb_bt/src/rm_behavior_tree/rm_behavior_tree/include/rm_behavior_tree/plugins/action", "program": "/home/<USER>/pb_bt/src/rm_behavior_tree/rm_behavior_tree/include/rm_behavior_tree/plugins/action/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}