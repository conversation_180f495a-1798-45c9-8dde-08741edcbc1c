import os

from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node

def generate_launch_description():
    use_sim_time = LaunchConfiguration('use_sim_time', default='true')

    fake_vel_transform_node = Node(
        package='fake_vel_transform',
        executable='fake_vel_transform_node',
        output='screen'
        #parameters=[
        #    {'use_sim_time': use_sim_time }
        #]
    )
    static_tf_node = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        # 参数顺序为：x y z roll pitch yaw parent_frame child_frame
        arguments=['0', '-0.12004', '0.00202', '0', '0', '0', 'livox_frame', 'base_link'],
    )
    
    ld = LaunchDescription([
        static_tf_node,
        fake_vel_transform_node,
    ])
    return ld
