# To use:
#
#     pre-commit run -a
#
# Or:
#
#     pre-commit install  # (runs every time you commit in git)
#
# To update this file:
#
#     pre-commit autoupdate
#
# See https://github.com/pre-commit/pre-commit

repos:
  # Standard hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-added-large-files
      - id: check-ast
      - id: check-case-conflict
      - id: check-docstring-first
      - id: check-merge-conflict
      - id: check-symlinks
      - id: check-xml
      - id: check-yaml
      - id: debug-statements
      - id: end-of-file-fixer
      - id: mixed-line-ending
      - id: trailing-whitespace
        exclude_types: [rst]
      - id: fix-byte-order-marker

  # Python hooks
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.19.1
    hooks:
    -   id: pyupgrade
        args: [--py36-plus]

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.8.4
    hooks:
      - id: ruff
        args: [ --fix ]
      - id: ruff-format

  # CPP hooks
  - repo: https://github.com/pre-commit/mirrors-clang-format
    rev: v14.0.3
    hooks:
      - id: clang-format
        args: ['-fallback-style=none', '-i']

  - repo: local
    hooks:
      - id: ament_cppcheck
        name: ament_cppcheck
        description: Static code analysis of C/C++ files.
        entry: env AMENT_CPPCHECK_ALLOW_SLOW_VERSIONS=1 ament_cppcheck
        language: system
        files: \.(h\+\+|h|hh|hxx|hpp|cuh|c|cc|cpp|cu|c\+\+|cxx|tpp|txx)$

  - repo: local
    hooks:
      - id: ament_cpplint
        name: ament_cpplint
        description: Static code analysis of C/C++ files.
        entry: ament_cpplint
        language: system
        files: \.(h\+\+|h|hh|hxx|hpp|cuh|c|cc|cpp|cu|c\+\+|cxx|tpp|txx)$
        args: ["--linelength=100", "--filter=-whitespace/newline"]

  # Cmake hooks
  - repo: local
    hooks:
      - id: ament_lint_cmake
        name: ament_lint_cmake
        description: Check format of CMakeLists.txt files.
        entry: ament_lint_cmake
        language: system
        files: CMakeLists\.txt$

  # Copyright
  - repo: local
    hooks:
      - id: ament_copyright
        name: ament_copyright
        description: Check if copyright notice is available in all files.
        entry: ament_copyright
        language: system

  # Docs - RestructuredText hooks
  - repo: https://github.com/PyCQA/doc8
    rev: v1.1.2
    hooks:
      - id: doc8
        args: ['--max-line-length=100', '--ignore=D001']
        exclude: CHANGELOG\.rst$

  - repo: https://github.com/pre-commit/pygrep-hooks
    rev: v1.10.0
    hooks:
      - id: rst-backticks
        exclude: CHANGELOG\.rst$
      - id: rst-directive-colons
      - id: rst-inline-touching-normal

  # Spellcheck in comments and docs
  # skipping of *.svg files is not working...
  - repo: https://github.com/codespell-project/codespell
    rev: v2.3.0
    hooks:
      - id: codespell
        args: ['--write-changes']
        exclude: CHANGELOG\.rst|\.(svg|pyc)$

  - repo: https://github.com/python-jsonschema/check-jsonschema
    rev: 0.30.0
    hooks:
      - id: check-github-workflows
        args: ["--verbose"]
      - id: check-github-actions
        args: ["--verbose"]
      - id: check-dependabot
        args: ["--verbose"]
