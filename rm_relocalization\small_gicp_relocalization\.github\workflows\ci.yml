name: Build
on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    container:
      image: rostooling/setup-ros-docker:ubuntu-jammy-ros-humble-desktop-latest
    steps:
      - name: Install small_gicp
        run: |
          git clone https://github.com/koide3/small_gicp.git
          sudo apt install -y libeigen3-dev libomp-dev
          cd small_gicp
          mkdir build && cd build
          cmake .. -DCMAKE_BUILD_TYPE=Release && make -j
          sudo make install

      - name: Build small_gicp_relocalization
        uses: ros-tooling/action-ros-ci@v0.3
        with:
          package-name: small_gicp_relocalization
          target-ros2-distro: humble
