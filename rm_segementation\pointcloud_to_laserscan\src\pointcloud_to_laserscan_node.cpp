/*
 * Software License Agreement (BSD License)
 *
 *  Copyright (c) 2010-2012, Willow Garage, Inc.
 *  All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *   * Redistributions of source code must retain the above copyright
 *     notice, this list of conditions and the following disclaimer.
 *   * Redistributions in binary form must reproduce the above
 *     copyright notice, this list of conditions and the following
 *     disclaimer in the documentation and/or other materials provided
 *     with the distribution.
 *   * Neither the name of Willow Garage, Inc. nor the names of its
 *     contributors may be used to endorse or promote products derived
 *     from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 *  FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 *  COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 *  INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 *  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 *  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 *  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 *  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
 *  ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 *  POSSIBILITY OF SUCH DAMAGE.
 *
 *
 */

/*
 * Author: Paul Bovbel
 */

#include "pointcloud_to_laserscan/pointcloud_to_laserscan_node.hpp"

#include <chrono>
#include <functional>
#include <limits>
#include <memory>
#include <string>
#include <thread>
#include <utility>
#include <deque>

#include "sensor_msgs/point_cloud2_iterator.hpp"

#include "tf2_ros/create_timer_ros.h"

#include <pcl/filters/voxel_grid.h>
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/kdtree/kdtree_flann.h>

namespace pointcloud_to_laserscan
{

PointCloudToLaserScanNode::PointCloudToLaserScanNode(const rclcpp::NodeOptions & options)
: rclcpp::Node("pointcloud_to_laserscan", options)
{
  target_frame_ = this->declare_parameter("target_frame", "");
  tolerance_ = this->declare_parameter("transform_tolerance", 0.01);
  // TODO(hidmic): adjust default input queue size based on actual concurrency levels
  // achievable by the associated executor
  input_queue_size_ = this->declare_parameter(
    "queue_size", static_cast<int>(std::thread::hardware_concurrency()));
  min_height_ = this->declare_parameter("min_height", std::numeric_limits<double>::min());
  max_height_ = this->declare_parameter("max_height", std::numeric_limits<double>::max());
  angle_min_ = this->declare_parameter("angle_min", -M_PI);
  angle_max_ = this->declare_parameter("angle_max", M_PI);
  angle_increment_ = this->declare_parameter("angle_increment", M_PI / 180.0);
  scan_time_ = this->declare_parameter("scan_time", 1.0 / 30.0);
  range_min_ = this->declare_parameter("range_min", 0.0);
  range_max_ = this->declare_parameter("range_max", std::numeric_limits<double>::max());
  inf_epsilon_ = this->declare_parameter("inf_epsilon", 1.0);
  use_inf_ = this->declare_parameter("use_inf", true);
  search_scale_ = this->declare_parameter("search_scale", 0.1); // 声明参数 search_scale，默认值为 0.1
  theta_ = this->declare_parameter("theta", 45.0); // 声明参数 theta，默认值为 45.0
  voxel_size_ = this->declare_parameter("voxel_size", 0.01); // 声明参数 voxel_size，默认值为 0.01
  alpha_ = this->declare_parameter("alpha", 45.0); // 声明参数 alpha，默认值为 45.0
  cout_ = this->declare_parameter("cout", 3);
  kdtree_radius_ = this->declare_parameter("kdtree_radius", 0.1); // 声明参数 kdtree_radius，默认值为 0.1
  Tolaser_min_height_ = this->declare_parameter("Tolaser_min_height", 0.0); // 声明参数 Tolaser_min_height，默认值为 0.0
  Tolaser_max_height_ = this->declare_parameter("Tolaser_max_height", 1.0); // 声明参数 Tolaser_max_height，默认值为 1.0

  // Retrieve new parameters
  double search_scale_ = this->get_parameter("search_scale").as_double();
  double theta_ = this->get_parameter("theta").as_double();
  double alpha_ = this->get_parameter("alpha").as_double();
  // Calculate r1, r2, and z based on the new parameters

  double r1_ = search_scale_;
  double r2_ = search_scale_ / sin(alpha_ * 3.14159 / 180.0);
  double z_  = search_scale_ *tan(theta_ * 3.14159 / 180.0) + search_scale_ /tan(alpha_ * 3.14159 / 180.0);
  // Retrieve voxel size parameter
  double voxel_size = this->get_parameter("voxel_size").as_double();

  pub_ = this->create_publisher<sensor_msgs::msg::LaserScan>("scan", rclcpp::SensorDataQoS());

  using std::placeholders::_1;
  // if pointcloud target frame specified, we need to filter by transform availability
  if (!target_frame_.empty()) {
    tf2_ = std::make_unique<tf2_ros::Buffer>(this->get_clock());
    auto timer_interface = std::make_shared<tf2_ros::CreateTimerROS>(
      this->get_node_base_interface(), this->get_node_timers_interface());
    tf2_->setCreateTimerInterface(timer_interface);
    tf2_listener_ = std::make_unique<tf2_ros::TransformListener>(*tf2_);
    message_filter_ = std::make_unique<MessageFilter>(
      sub_, *tf2_, target_frame_, input_queue_size_,
      this->get_node_logging_interface(),
      this->get_node_clock_interface());
    message_filter_->registerCallback(
      std::bind(&PointCloudToLaserScanNode::cloudCallback, this, _1));
  } else {  // otherwise setup direct subscription
    sub_.registerCallback(std::bind(&PointCloudToLaserScanNode::cloudCallback, this, _1));
  }

  subscription_listener_thread_ = std::thread(
    std::bind(&PointCloudToLaserScanNode::subscriptionListenerThreadLoop, this));
}

PointCloudToLaserScanNode::~PointCloudToLaserScanNode()
{
  alive_.store(false);
  subscription_listener_thread_.join();
}

void PointCloudToLaserScanNode::subscriptionListenerThreadLoop()
{
  rclcpp::Context::SharedPtr context = this->get_node_base_interface()->get_context();

  const std::chrono::milliseconds timeout(100);
  while (rclcpp::ok(context) && alive_.load()) {
    int subscription_count = pub_->get_subscription_count() +
      pub_->get_intra_process_subscription_count();
    if (subscription_count > 0) {
      if (!sub_.getSubscriber()) {
        RCLCPP_INFO(
          this->get_logger(),
          "Got a subscriber to laserscan, starting pointcloud subscriber");
        rclcpp::SensorDataQoS qos;
        qos.keep_last(input_queue_size_);
        sub_.subscribe(this, "cloud_in", qos.get_rmw_qos_profile());
      }
    } else if (sub_.getSubscriber()) {
      RCLCPP_INFO(
        this->get_logger(),
        "No subscribers to laserscan, shutting down pointcloud subscriber");
      sub_.unsubscribe();
    }
    rclcpp::Event::SharedPtr event = this->get_graph_event();
    this->wait_for_graph_change(event, timeout);
  }
  sub_.unsubscribe();
}

void PointCloudToLaserScanNode::cloudCallback(
  sensor_msgs::msg::PointCloud2::ConstSharedPtr cloud_msg)
{
  // build laserscan output
  auto scan_msg = std::make_unique<sensor_msgs::msg::LaserScan>();
  scan_msg->header = cloud_msg->header;
  if (!target_frame_.empty()) {
    scan_msg->header.frame_id = target_frame_;
  }

  scan_msg->angle_min = angle_min_;
  scan_msg->angle_max = angle_max_;
  scan_msg->angle_increment = angle_increment_;
  scan_msg->time_increment = 0.0;
  scan_msg->scan_time = scan_time_;
  scan_msg->range_min = range_min_;
  scan_msg->range_max = range_max_;

  // determine amount of rays to create
  uint32_t ranges_size = std::ceil(
    (scan_msg->angle_max - scan_msg->angle_min) / scan_msg->angle_increment);

  if (use_inf_) {
    scan_msg->ranges.assign(ranges_size, std::numeric_limits<double>::infinity());
  } else {
    scan_msg->ranges.assign(ranges_size, scan_msg->range_max + inf_epsilon_);
  }

  // Transform cloud if necessary
  if (scan_msg->header.frame_id != cloud_msg->header.frame_id) {
    try {
      auto cloud = std::make_shared<sensor_msgs::msg::PointCloud2>();
      tf2_->transform(*cloud_msg, *cloud, target_frame_, tf2::durationFromSec(tolerance_));
      cloud_msg = cloud;
    } catch (tf2::TransformException & ex) {
      RCLCPP_ERROR_STREAM(this->get_logger(), "Transform failure: " << ex.what());
      return;
    }
  }

  // Downsample the point cloud using voxel grid filter
  pcl::PCLPointCloud2::Ptr pcl_cloud(new pcl::PCLPointCloud2());
  pcl::PCLPointCloud2::Ptr pcl_filtered_cloud(new pcl::PCLPointCloud2());
  pcl_conversions::toPCL(*cloud_msg, *pcl_cloud);

  double voxel_size = this->get_parameter("voxel_size").as_double();

  pcl::VoxelGrid<pcl::PCLPointCloud2> voxel_filter;
  voxel_filter.setInputCloud(pcl_cloud);
  voxel_filter.setLeafSize(voxel_size, voxel_size, voxel_size);
  voxel_filter.filter(*pcl_filtered_cloud);

  sensor_msgs::msg::PointCloud2 filtered_cloud_msg;
  pcl_conversions::fromPCL(*pcl_filtered_cloud, filtered_cloud_msg);

  // Convert PointCloud2 to PCL PointCloud
  pcl::PointCloud<pcl::PointXYZ>::Ptr pcl_cloud_xyz(new pcl::PointCloud<pcl::PointXYZ>());
  pcl::fromROSMsg(filtered_cloud_msg, *pcl_cloud_xyz);

  // Create a KDTree for fast neighbor search
  pcl::KdTreeFLANN<pcl::PointXYZ> kdtree;
  kdtree.setInputCloud(pcl_cloud_xyz);

  pcl::PointCloud<pcl::PointXYZ>::Ptr filtered_cloud(new pcl::PointCloud<pcl::PointXYZ>());

  double search_scale = this->get_parameter("search_scale").as_double();
  double theta = this->get_parameter("theta").as_double();
  double alpha = this->get_parameter("alpha").as_double();
  // Calculate r1, r2, and z based on the new parameters
  double r1 = search_scale;
  double r2 = search_scale / sin(alpha * 3.14159 / 180.0);
  double z  = search_scale *tan(theta * 3.14159 / 180.0) + search_scale /tan(alpha * 3.14159 / 180.0);

  for (const auto & point : pcl_cloud_xyz->points) {
    std::vector<int> point_indices;
    std::vector<float> point_distances;

    // Search for neighbors within kdtree_radius r1
    if (kdtree.radiusSearch(point, r1, point_indices, point_distances) > 1) {
      bool valid = false;
      for (const auto & idx : point_indices) {
        const auto & neighbor = pcl_cloud_xyz->points[idx];
        double horizontal_distance = std::hypot(neighbor.x - point.x, neighbor.y - point.y);
        double vertical_distance = std::abs(neighbor.z - (point.z + z));

        if (horizontal_distance <= r1 && vertical_distance <= r2) {
          valid = true;
          break;
        }
      }

      if (valid) {
        filtered_cloud->points.push_back(point);
      }
    }
  }

  // Convert filtered PCL PointCloud back to PointCloud2
  pcl::toROSMsg(*filtered_cloud, filtered_cloud_msg);
  filtered_cloud_msg.header = cloud_msg->header;

  // Iterate through pointcloud
  bool has_valid_data = false;
  for (sensor_msgs::PointCloud2ConstIterator<float> iter_x(filtered_cloud_msg, "x"),
    iter_y(filtered_cloud_msg, "y"), iter_z(filtered_cloud_msg, "z");
    iter_x != iter_x.end(); ++iter_x, ++iter_y, ++iter_z)
  {
    if (std::isnan(*iter_x) || std::isnan(*iter_y) || std::isnan(*iter_z)) {
      RCLCPP_DEBUG(
        this->get_logger(),
        "rejected for nan in point(%f, %f, %f)\n",
        *iter_x, *iter_y, *iter_z);
      continue;
    }

    if (*iter_z > max_height_ || *iter_z < min_height_) {
      RCLCPP_DEBUG(
        this->get_logger(),
        "rejected for height %f not in range (%f, %f)\n",
        *iter_z, min_height_, max_height_);
      continue;
    }

    // Add filtering logic for Tolaser_min_height and Tolaser_max_height
    if (*iter_z < Tolaser_min_height_ || *iter_z > Tolaser_max_height_) {
      RCLCPP_DEBUG(
        this->get_logger(),
        "rejected for height %f not in Tolaser range (%f, %f)\n",
        *iter_z, Tolaser_min_height_, Tolaser_max_height_);
      continue;
    }

    double range = hypot(*iter_x, *iter_y);
    if (range < range_min_) {
      RCLCPP_DEBUG(
        this->get_logger(),
        "rejected for range %f below minimum value %f. Point: (%f, %f, %f)",
        range, range_min_, *iter_x, *iter_y, *iter_z);
      continue;
    }
    if (range > range_max_) {
      RCLCPP_DEBUG(
        this->get_logger(),
        "rejected for range %f above maximum value %f. Point: (%f, %f, %f)",
        range, range_max_, *iter_x, *iter_y, *iter_z);
      continue;
    }

    double angle = atan2(*iter_y, *iter_x);
    if (angle < scan_msg->angle_min || angle > scan_msg->angle_max) {
      RCLCPP_DEBUG(
        this->get_logger(),
        "rejected for angle %f not in range (%f, %f)\n",
        angle, scan_msg->angle_min, scan_msg->angle_max);
      continue;
    }

    // overwrite range at laserscan ray if new range is smaller
    int index = (angle - scan_msg->angle_min) / scan_msg->angle_increment;
    if (range < scan_msg->ranges[index]) {
      scan_msg->ranges[index] = range;
      has_valid_data = true;
    }
  }

  // Retrieve the cout parameter
  cout_ = this->get_parameter("cout").as_int();

  // Ensure cout is at least 1
  if (cout_ < 1) {
    RCLCPP_WARN(this->get_logger(), "Parameter 'cout' must be at least 1. Setting it to 1.");
    cout_ = 1;
  }

  // Store the current scan message
  if (previous_scans_.size() >= static_cast<size_t>(cout_)) {
    previous_scans_.pop_front();
  }
  previous_scans_.push_back(std::move(scan_msg));

  // Create a new scan message to hold the fused data
  auto fused_scan_msg = std::make_unique<sensor_msgs::msg::LaserScan>(*previous_scans_.back());

  // Fuse the current scan with previous scans
  for (const auto & prev_scan : previous_scans_) {
    for (size_t i = 0; i < fused_scan_msg->ranges.size(); ++i) {
      fused_scan_msg->ranges[i] = std::min(fused_scan_msg->ranges[i], prev_scan->ranges[i]);
    }
  }

  // Only publish if there is valid data
  if (has_valid_data) {
    pub_->publish(std::move(fused_scan_msg));
  }
}

void PointCloudToLaserScanNode::processScanMessage(sensor_msgs::msg::LaserScan::SharedPtr scan_msg) {
    // Retrieve the kdtree_radius parameter
    double kdtree_radius = this->get_parameter("kdtree_radius").as_double();

    // Convert LaserScan to PointCloud
    pcl::PointCloud<pcl::PointXYZ>::Ptr pcl_cloud(new pcl::PointCloud<pcl::PointXYZ>());
    for (size_t i = 0; i < scan_msg->ranges.size(); ++i) {
        double angle = scan_msg->angle_min + i * scan_msg->angle_increment;
        double range = scan_msg->ranges[i];
        if (std::isfinite(range)) {
            pcl::PointXYZ point;
            point.x = range * cos(angle);
            point.y = range * sin(angle);
            point.z = 0.0; // Assuming 2D scan
            pcl_cloud->points.push_back(point);
        }
    }

    // Create a KDTree for fast neighbor search
    pcl::KdTreeFLANN<pcl::PointXYZ> kdtree;
    kdtree.setInputCloud(pcl_cloud);

    pcl::PointCloud<pcl::PointXYZ>::Ptr filtered_cloud(new pcl::PointCloud<pcl::PointXYZ>());

    for (const auto & point : pcl_cloud->points) {
        std::vector<int> point_indices;
        std::vector<float> point_distances;

        // Search for neighbors within the kdtree_radius
        if (kdtree.radiusSearch(point, kdtree_radius, point_indices, point_distances) > 1) {
            filtered_cloud->points.push_back(point);
        }
    }

    // Convert filtered PointCloud back to LaserScan
    for (size_t i = 0; i < scan_msg->ranges.size(); ++i) {
        double angle = scan_msg->angle_min + i * scan_msg->angle_increment;
        bool valid = false;
        for (const auto & point : filtered_cloud->points) {
            double point_angle = atan2(point.y, point.x);
            if (fabs(point_angle - angle) < scan_msg->angle_increment / 2) {
                scan_msg->ranges[i] = sqrt(point.x * point.x + point.y * point.y);
                valid = true;
                break;
            }
        }
        if (!valid) {
            scan_msg->ranges[i] = std::numeric_limits<float>::infinity();
        }
    }
}

}  // namespace pointcloud_to_laserscan

#include "rclcpp_components/register_node_macro.hpp"

RCLCPP_COMPONENTS_REGISTER_NODE(pointcloud_to_laserscan::PointCloudToLaserScanNode)
