{"configurations": [{"browse": {"databaseFilename": "${default}", "limitSymbolsToIncludedHeaders": false}, "includePath": ["/home/<USER>/rm/install/teb_local_planner/include/**", "/home/<USER>/rm/install/teb_msgs/include/**", "/home/<USER>/rm/install/rm_serial_driver/include/**", "/home/<USER>/rm/install/rm_lifecycle_manager/include/**", "/home/<USER>/rm/install/pointcloud_to_laserscan/include/**", "/home/<USER>/rm/install/octomap_server/include/**", "/home/<USER>/rm/install/nav2_xms_bringup/include/**", "/home/<USER>/rm/install/linefit_ground_segmentation/include/**", "/home/<USER>/rm/install/icp_registration/include/**", "/home/<USER>/rm/install/icp_localization_ros2/include/**", "/home/<USER>/rm/install/gimbal_command_publisher/include/**", "/home/<USER>/rm/install/fake_vel_transform/include/**", "/home/<USER>/rm/install/energy_processor/include/**", "/home/<USER>/rm/install/energy_detector/include/**", "/home/<USER>/rm/install/energy_interfaces/include/**", "/home/<USER>/rm/install/costmap_converter/include/**", "/home/<USER>/rm/install/costmap_converter_msgs/include/**", "/home/<USER>/rm/install/armor_processor/include/**", "/home/<USER>/rm/install/armor_detector/include/**", "/home/<USER>/rm/install/auto_aim_interfaces/include/**", "/home/<USER>/rm/install/2d_slam_xms/include/**", "/home/<USER>/BehaviorTree.ROS2/install/rm_behavior_xms/include/**", "/home/<USER>/BehaviorTree.ROS2/install/behaviortree_ros2/include/**", "/home/<USER>/BehaviorTree.ROS2/install/btcpp_ros2_interfaces/include/**", "/home/<USER>/sentry/install/rm_serial_driver/include/**", "/home/<USER>/sentry/install/rm_lifecycle_manager/include/**", "/home/<USER>/sentry/install/gimbal_command_publisher/include/**", "/home/<USER>/sentry/install/galaxy_camera/include/**", "/home/<USER>/sentry/install/energy_processor/include/**", "/home/<USER>/sentry/install/energy_detector/include/**", "/home/<USER>/sentry/install/energy_interfaces/include/**", "/home/<USER>/sentry/install/armor_processor/include/**", "/home/<USER>/sentry/install/armor_detector/include/**", "/home/<USER>/sentry/install/auto_aim_interfaces/include/**", "/home/<USER>/ws_livox/install/fast_lio/include/**", "/home/<USER>/ws_livox/install/livox_ros_driver2/include/**", "/opt/ros/humble/include/**", "/home/<USER>/ws_livox/src/fast_lio/include/**", "/usr/include/**"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++14"}], "version": 4}