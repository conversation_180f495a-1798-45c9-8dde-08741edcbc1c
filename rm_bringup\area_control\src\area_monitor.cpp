#include "area_control/area_monitor.hpp"
#include <geometry_msgs/msg/transform_stamped.hpp>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>

#include <iostream>
#include <regex>
#include <sstream>

AreaMonitor::AreaMonitor()
: Node("area_monitor")
{
  // 创建TF监听器
  tf_buffer_ = std::make_shared<tf2_ros::Buffer>(this->get_clock());
  tf_listener_ = std::make_shared<tf2_ros::TransformListener>(*tf_buffer_);
  
  // 创建话题发布器
  pub_scan_ = create_publisher<std_msgs::msg::Float32>("/if_pub_ms200_scan", 10);
  pub_vel_ = create_publisher<std_msgs::msg::UInt8>("/vel_control", 10);
  
  // 从启动参数获取区域配置
  std::vector<std::string> area_strs;
  declare_parameter("areas", std::vector<std::string>{});
  get_parameter("areas", area_strs);
  
  // 解析区域参数
  parseAreas(area_strs);
  
  // 创建定时器，定期检查机器人位置
  timer_ = create_wall_timer(std::chrono::milliseconds(100), 
                             std::bind(&AreaMonitor::checkRobotPosition, this));
  
  RCLCPP_INFO(get_logger(), "区域监控节点已启动，共加载 %zu 个区域", areas_.size());
}

void AreaMonitor::parseAreas(const std::vector<std::string>& area_strs)
{
  areas_.clear();
  area_names_.clear();
  
  for (size_t i = 0; i < area_strs.size(); ++i) {
    const auto& area_str = area_strs[i];
    Polygon polygon;
    std::regex point_pattern("\\{(\\d+):([\\d.-]+),([\\d.-]+)\\}");
    std::smatch matches;
    std::string::const_iterator search_start(area_str.cbegin());
    
    while (std::regex_search(search_start, area_str.cend(), matches, point_pattern)) {
      geometry_msgs::msg::Point point;
      point.x = std::stod(matches[2].str());
      point.y = std::stod(matches[3].str());
      point.z = 0.0;
      polygon.push_back(point);
      
      search_start = matches.suffix().first;
    }
    
    if (polygon.size() >= 3) {
      areas_.push_back(polygon);
      area_names_.push_back("区域" + std::to_string(i + 1));
      
      std::stringstream ss;
      ss << area_names_.back() << " 包含 " << polygon.size() << " 个点: ";
      for (const auto& p : polygon) {
        ss << "(" << p.x << ", " << p.y << ") ";
      }
      RCLCPP_INFO(get_logger(), "%s", ss.str().c_str());
    }
  }
}

bool AreaMonitor::isPointInPolygon(const geometry_msgs::msg::Point& point, const Polygon& polygon)
{
  if (polygon.size() < 3) return false;
  
  bool inside = false;
  size_t j = polygon.size() - 1;
  
  for (size_t i = 0; i < polygon.size(); i++) {
    if ((polygon[i].y > point.y) != (polygon[j].y > point.y) &&
        (point.x < (polygon[j].x - polygon[i].x) * (point.y - polygon[i].y) /
                    (polygon[j].y - polygon[i].y) + polygon[i].x)) {
      inside = !inside;
    }
    j = i;
  }
  
  return inside;
}

void AreaMonitor::checkRobotPosition()
{
  try {
    // 获取base_link在map坐标系下的位置
    geometry_msgs::msg::TransformStamped transform;
    transform = tf_buffer_->lookupTransform("map", "base_link", tf2::TimePointZero);
    
    // 获取机器人位置
    geometry_msgs::msg::Point robot_pos;
    robot_pos.x = transform.transform.translation.x;
    robot_pos.y = transform.transform.translation.y;
    robot_pos.z = transform.transform.translation.z;
    
    RCLCPP_INFO(get_logger(), "机器人位置: (%.2f, %.2f, %.2f)", 
                robot_pos.x, robot_pos.y, robot_pos.z);
    
    // 检查机器人在哪个区域内
    bool in_any_area = false;
    
    for (size_t i = 0; i < areas_.size(); ++i) {
      if (isPointInPolygon(robot_pos, areas_[i])) {
        RCLCPP_INFO(get_logger(), "机器人当前位于%s内", area_names_[i].c_str());
        in_any_area = true;
      }
    }
    
    if (!in_any_area) {
      RCLCPP_INFO(get_logger(), "机器人不在任何已配置区域内");
    }
    
    // 根据机器人位置发布消息
    std_msgs::msg::Float32 scan_msg;
    std_msgs::msg::UInt8 vel_msg;
    
    // 当机器人处于区域1或区域2内时
    if (in_any_area) {
      scan_msg.data = 0.0;  // 在区域内，发布0.0
      vel_msg.data = 0;     // 在区域内，速度控制为0
    } else {
      scan_msg.data = 1.0;  // 不在区域内，发布1.0
      vel_msg.data = 1;     // 不在区域内，速度控制为1
    }
    
    // 发布消息
    pub_scan_->publish(scan_msg);
    pub_vel_->publish(vel_msg);
    
    RCLCPP_INFO(get_logger(), "发布: /if_pub_ms200_scan = %.1f, /vel_control = %d", 
                scan_msg.data, vel_msg.data);
    
  } catch (tf2::TransformException& ex) {
    RCLCPP_ERROR(get_logger(), "无法获取机器人位置: %s", ex.what());
  }
}

int main(int argc, char* argv[])
{
  rclcpp::init(argc, argv);
  auto node = std::make_shared<AreaMonitor>();
  rclcpp::spin(node);
  rclcpp::shutdown();
  return 0;
}
