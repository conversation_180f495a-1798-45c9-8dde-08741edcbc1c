<?xml version="1.0"?>
<package format="2">
  <name>imu_complementary_filter</name>
  <version>2.1.3</version>
  <description>Filter which fuses angular velocities, accelerations, and (optionally) magnetic readings from a generic IMU device into a quaternion to represent the orientation of the device wrt the global frame. Based on the algorithm by <PERSON> etal. described in the paper "Keeping a Good Attitude: A Quaternion-Based Orientation Filter for IMUs and MARGs" available at http://www.mdpi.com/1424-8220/15/8/19302 .</description>

  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>BSD</license>

  <url>http://www.mdpi.com/1424-8220/15/8/19302</url>
  <author email="<EMAIL>"><PERSON></author>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>message_filters</build_depend>
  <build_depend>rclcpp</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>tf2</build_depend>
  <build_depend>tf2_ros</build_depend>
  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
