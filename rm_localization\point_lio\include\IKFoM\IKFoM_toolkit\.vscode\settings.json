{"files.associations": {"iosfwd": "cpp", "core": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "array": "cpp", "atomic": "cpp", "strstream": "cpp", "*.tcc": "cpp", "bitset": "cpp", "chrono": "cpp", "complex": "cpp", "cstdint": "cpp", "deque": "cpp", "list": "cpp", "unordered_map": "cpp", "vector": "cpp", "exception": "cpp", "fstream": "cpp", "functional": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "new": "cpp", "ostream": "cpp", "numeric": "cpp", "ratio": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "system_error": "cpp", "thread": "cpp", "cfenv": "cpp", "cinttypes": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "typeinfo": "cpp", "algorithm": "cpp", "iterator": "cpp", "map": "cpp", "memory": "cpp", "memory_resource": "cpp", "optional": "cpp", "random": "cpp", "set": "cpp", "string": "cpp", "string_view": "cpp"}}