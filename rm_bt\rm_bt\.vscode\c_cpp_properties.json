{"configurations": [{"browse": {"databaseFilename": "${default}", "limitSymbolsToIncludedHeaders": false}, "includePath": ["/home/<USER>/jl_rm_bt/install/rm_bt/include/**", "/home/<USER>/jl_rm_bt/install/rm_sentry_decision_interfaces/include/**", "/home/<USER>/jl_rm_bt/install/btcpp_ros2_interfaces/include/**", "/home/<USER>/jl_rm_bt/install/behaviortree_ros2/include/**", "/home/<USER>/rm/install/teb_local_planner/include/**", "/home/<USER>/rm/install/teb_msgs/include/**", "/home/<USER>/rm/install/rm_serial_driver/include/**", "/home/<USER>/rm/install/rm_sentry_decision_interfaces/include/**", "/home/<USER>/rm/install/rm_lifecycle_manager/include/**", "/home/<USER>/rm/install/pointcloud_to_laserscan/include/**", "/home/<USER>/rm/install/octomap_server/include/**", "/home/<USER>/rm/install/nav2_xms_bringup/include/**", "/home/<USER>/rm/install/linefit_ground_segmentation/include/**", "/home/<USER>/rm/install/icp_registration/include/**", "/home/<USER>/rm/install/icp_localization_ros2/include/**", "/home/<USER>/rm/install/gimbal_command_publisher/include/**", "/home/<USER>/rm/install/fake_vel_transform/include/**", "/home/<USER>/rm/install/energy_processor/include/**", "/home/<USER>/rm/install/energy_detector/include/**", "/home/<USER>/rm/install/energy_interfaces/include/**", "/home/<USER>/rm/install/costmap_converter/include/**", "/home/<USER>/rm/install/costmap_converter_msgs/include/**", "/home/<USER>/rm/install/armor_processor/include/**", "/home/<USER>/rm/install/armor_detector/include/**", "/home/<USER>/rm/install/auto_aim_interfaces/include/**", "/home/<USER>/rm/install/2d_slam_xms/include/**", "/home/<USER>/ws_livox/install/fast_lio/include/**", "/home/<USER>/ws_livox/install/livox_ros_driver2/include/**", "/opt/ros/humble/include/**", "/home/<USER>/jl_rm_bt/src/BehaviorTree.ROS2/behaviortree_ros2/include/**", "/home/<USER>/jl_rm_bt/src/rm_bt/include/**", "/home/<USER>/jl_rm_bt/src/rm_sentry_decision_interfaces/include/**", "include/**", "/usr/include/**"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++14"}], "version": 4}