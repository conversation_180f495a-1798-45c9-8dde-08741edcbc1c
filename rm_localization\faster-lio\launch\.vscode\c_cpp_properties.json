{"configurations": [{"browse": {"databaseFilename": "${default}", "limitSymbolsToIncludedHeaders": false}, "includePath": ["/home/<USER>/NUDT_navigation/install/teb_local_planner/include/**", "/home/<USER>/NUDT_navigation/install/teb_msgs/include/**", "/home/<USER>/NUDT_navigation/install/small_gicp_relocalization/include/**", "/home/<USER>/NUDT_navigation/install/rm_serial_driver/include/**", "/home/<USER>/NUDT_navigation/install/rm_lifecycle_manager/include/**", "/home/<USER>/NUDT_navigation/install/pointcloud_to_laserscan/include/**", "/home/<USER>/NUDT_navigation/install/oradar_lidar/include/**", "/home/<USER>/NUDT_navigation/install/icp_registration/include/**", "/home/<USER>/NUDT_navigation/install/faster_lio/include/**", "/home/<USER>/NUDT_navigation/install/fast_lio/include/**", "/home/<USER>/NUDT_navigation/install/livox_ros_driver2/include/**", "/home/<USER>/NUDT_navigation/install/linefit_ground_segmentation/include/**", "/home/<USER>/NUDT_navigation/install/jl_nav2_bringup/include/**", "/home/<USER>/NUDT_navigation/install/imu_complementary_filter/include/**", "/home/<USER>/NUDT_navigation/install/gimbal_command_publisher/include/**", "/home/<USER>/NUDT_navigation/install/fake_vel_transform/include/**", "/home/<USER>/NUDT_navigation/install/energy_processor/include/**", "/home/<USER>/NUDT_navigation/install/energy_detector/include/**", "/home/<USER>/NUDT_navigation/install/energy_interfaces/include/**", "/home/<USER>/NUDT_navigation/install/costmap_converter/include/**", "/home/<USER>/NUDT_navigation/install/costmap_converter_msgs/include/**", "/home/<USER>/NUDT_navigation/install/armor_processor/include/**", "/home/<USER>/NUDT_navigation/install/armor_detector/include/**", "/home/<USER>/NUDT_navigation/install/auto_aim_interfaces/include/**", "/home/<USER>/NUDT_navigation/install/2d_slam/include/**", "/home/<USER>/NUDT_bt/install/rm_bt/include/**", "/home/<USER>/NUDT_bt/install/rm_sentry_decision_interfaces/include/**", "/home/<USER>/NUDT_bt/install/btcpp_ros2_interfaces/include/**", "/home/<USER>/NUDT_bt/install/behaviortree_ros2/include/**", "/opt/ros/humble/include/**", "/home/<USER>/NUDT_navigation/src/rm_localization/faster-lio/include/**", "/usr/include/**"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++14"}], "version": 4}