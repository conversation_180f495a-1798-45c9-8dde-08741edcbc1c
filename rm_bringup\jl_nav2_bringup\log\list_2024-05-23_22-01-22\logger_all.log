[0.759s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'list', '-p', '--base-paths', '/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup']
[0.760s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='list', build_base='build', ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], topological_order=False, names_only=False, paths_only=True, topological_graph=False, topological_graph_dot=False, topological_graph_density=False, topological_graph_legend=False, topological_graph_dot_cluster=False, topological_graph_dot_include_skipped=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7e94039ad0c0>, verb_extension=<colcon_package_information.verb.list.ListVerb object at 0x7e9403da5f60>, main=<bound method ListVerb.main of <colcon_package_information.verb.list.ListVerb object at 0x7e9403da5f60>>)
[0.811s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.812s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.812s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.812s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.812s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.812s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup'
[0.812s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup) by extensions ['ignore', 'ignore_ament_install']
[0.812s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup) by extension 'ignore'
[0.812s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup) by extension 'ignore_ament_install'
[0.812s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup) by extensions ['colcon_pkg']
[0.812s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup) by extension 'colcon_pkg'
[0.812s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup) by extensions ['colcon_meta']
[0.812s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup) by extension 'colcon_meta'
[0.812s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup) by extensions ['ros']
[0.812s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup) by extension 'ros'
[0.824s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup) by extensions ['cmake', 'python']
[0.824s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup) by extension 'cmake'
[0.824s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup) by extension 'python'
[0.824s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup) by extensions ['python_setup_py']
[0.824s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup) by extension 'python_setup_py'
[0.824s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/log) by extensions ['ignore', 'ignore_ament_install']
[0.824s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/log) by extension 'ignore'
[0.825s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/log) ignored
[0.825s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/src) by extensions ['ignore', 'ignore_ament_install']
[0.825s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/src) by extension 'ignore'
[0.825s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/src) by extension 'ignore_ament_install'
[0.825s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/src) by extensions ['colcon_pkg']
[0.825s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/src) by extension 'colcon_pkg'
[0.825s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/src) by extensions ['colcon_meta']
[0.825s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/src) by extension 'colcon_meta'
[0.825s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/src) by extensions ['ros']
[0.825s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/src) by extension 'ros'
[0.828s] DEBUG:colcon.colcon_core.package_identification:Package '/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/src' with type 'ros.ament_cmake' and name 'jl_nav2_bringup'
[0.828s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party) by extensions ['ignore', 'ignore_ament_install']
[0.828s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party) by extension 'ignore'
[0.828s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party) by extension 'ignore_ament_install'
[0.828s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party) by extensions ['colcon_pkg']
[0.828s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party) by extension 'colcon_pkg'
[0.828s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party) by extensions ['colcon_meta']
[0.828s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party) by extension 'colcon_meta'
[0.828s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party) by extensions ['ros']
[0.828s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party) by extension 'ros'
[0.828s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party) by extensions ['cmake', 'python']
[0.828s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party) by extension 'cmake'
[0.828s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party) by extension 'python'
[0.828s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party) by extensions ['python_setup_py']
[0.828s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party) by extension 'python_setup_py'
[0.829s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter) by extensions ['ignore', 'ignore_ament_install']
[0.829s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter) by extension 'ignore'
[0.829s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter) by extension 'ignore_ament_install'
[0.829s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter) by extensions ['colcon_pkg']
[0.829s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter) by extension 'colcon_pkg'
[0.829s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter) by extensions ['colcon_meta']
[0.829s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter) by extension 'colcon_meta'
[0.829s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter) by extensions ['ros']
[0.829s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter) by extension 'ros'
[0.830s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter) by extensions ['cmake', 'python']
[0.830s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter) by extension 'cmake'
[0.830s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter) by extension 'python'
[0.830s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter) by extensions ['python_setup_py']
[0.830s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter) by extension 'python_setup_py'
[0.830s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter) by extensions ['ignore', 'ignore_ament_install']
[0.830s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter) by extension 'ignore'
[0.830s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter) by extension 'ignore_ament_install'
[0.830s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter) by extensions ['colcon_pkg']
[0.830s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter) by extension 'colcon_pkg'
[0.830s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter) by extensions ['colcon_meta']
[0.830s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter) by extension 'colcon_meta'
[0.830s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter) by extensions ['ros']
[0.830s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter) by extension 'ros'
[0.832s] DEBUG:colcon.colcon_core.package_identification:Package '/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter' with type 'ros.ament_cmake' and name 'costmap_converter'
[0.832s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.832s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter_msgs) by extension 'ignore'
[0.833s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter_msgs) by extension 'ignore_ament_install'
[0.833s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter_msgs) by extensions ['colcon_pkg']
[0.833s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter_msgs) by extension 'colcon_pkg'
[0.833s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter_msgs) by extensions ['colcon_meta']
[0.833s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter_msgs) by extension 'colcon_meta'
[0.833s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter_msgs) by extensions ['ros']
[0.833s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter_msgs) by extension 'ros'
[0.834s] DEBUG:colcon.colcon_core.package_identification:Package '/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/costmap_converter/costmap_converter_msgs' with type 'ros.ament_cmake' and name 'costmap_converter_msgs'
[0.834s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner) by extensions ['ignore', 'ignore_ament_install']
[0.834s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner) by extension 'ignore'
[0.834s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner) by extension 'ignore_ament_install'
[0.834s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner) by extensions ['colcon_pkg']
[0.834s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner) by extension 'colcon_pkg'
[0.834s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner) by extensions ['colcon_meta']
[0.834s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner) by extension 'colcon_meta'
[0.834s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner) by extensions ['ros']
[0.834s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner) by extension 'ros'
[0.834s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner) by extensions ['cmake', 'python']
[0.834s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner) by extension 'cmake'
[0.834s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner) by extension 'python'
[0.834s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner) by extensions ['python_setup_py']
[0.834s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner) by extension 'python_setup_py'
[0.835s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_local_planner) by extensions ['ignore', 'ignore_ament_install']
[0.835s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_local_planner) by extension 'ignore'
[0.835s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_local_planner) by extension 'ignore_ament_install'
[0.835s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_local_planner) by extensions ['colcon_pkg']
[0.835s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_local_planner) by extension 'colcon_pkg'
[0.835s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_local_planner) by extensions ['colcon_meta']
[0.835s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_local_planner) by extension 'colcon_meta'
[0.835s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_local_planner) by extensions ['ros']
[0.835s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_local_planner) by extension 'ros'
[0.837s] DEBUG:colcon.colcon_core.package_identification:Package '/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_local_planner' with type 'ros.ament_cmake' and name 'teb_local_planner'
[0.838s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.838s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_msgs) by extension 'ignore'
[0.838s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_msgs) by extension 'ignore_ament_install'
[0.838s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_msgs) by extensions ['colcon_pkg']
[0.838s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_msgs) by extension 'colcon_pkg'
[0.838s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_msgs) by extensions ['colcon_meta']
[0.838s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_msgs) by extension 'colcon_meta'
[0.838s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_msgs) by extensions ['ros']
[0.838s] Level 1:colcon.colcon_core.package_identification:_identify(/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_msgs) by extension 'ros'
[0.839s] DEBUG:colcon.colcon_core.package_identification:Package '/home/<USER>/rm/src/rm_nav2_2204/jl_nav2_bringup/third_party/teb_local_planner/teb_msgs' with type 'ros.ament_cmake' and name 'teb_msgs'
[0.839s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
