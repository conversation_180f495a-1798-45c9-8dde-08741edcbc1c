ground_segmentation:
  ros__parameters:
    n_threads: 5                # number of threads to use.

    r_min: 1.0                # minimum point distance.
    r_max: 50.0                  # maximum point distance.
    n_bins: 140                 # number of radial bins.
    n_segments: 360            # number of radial segments.

    max_dist_to_line: 0.8 #0.1      # maximum vertical distance of point to line to be considered ground.

    sensor_height: 0.45 #-0.5         # sensor height above ground.
    min_slope: -0.2              # minimum slope of a ground line.
    max_slope: 1.5 #40度坡度对应的正切值，不超过40度的坡会被视为平面              # maximum slope of a ground line.
    max_fit_error: 0.075         # maximum error of a point during line fit.
    long_threshold: 1.0         # distance between points after which they are considered far from each other.
    max_long_height: 0.1        # maximum height change to previous point in long line.
    max_start_height: 0.2       # maximum difference to estimated ground height to start a new line.
    line_search_angle: 0.3 #0.1      # how far to search in angular direction to find a line [rad].

    gravity_aligned_frame: ""   # Frame which has its z axis aligned with gravity. (Sensor frame if empty.)

    latch: false                # latch output topics or not
    visualize: false            # visualize segmentation result - USE ONLY FOR DEBUGGING

    input_topic: "/cloud_registered_body" #"/livox/lidar/pointcloud" #"cloud_registered_body"
    obstacle_output_topic: "segmentation/obstacle"
    ground_output_topic: "segmentation/ground"
