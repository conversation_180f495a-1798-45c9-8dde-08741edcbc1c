#ifndef AREA_CONTROL__AREA_MONITOR_HPP_
#define AREA_CONTROL__AREA_MONITOR_HPP_

#include <rclcpp/rclcpp.hpp>
#include <tf2_ros/buffer.h>
#include <tf2_ros/transform_listener.h>
#include <geometry_msgs/msg/point.hpp>
#include <std_msgs/msg/float32.hpp>
#include <std_msgs/msg/u_int8.hpp>

#include <vector>
#include <string>
#include <memory>
#include <regex>

class AreaMonitor : public rclcpp::Node
{
public:
  AreaMonitor();

private:
  // 多边形区域的点集
  using Polygon = std::vector<geometry_msgs::msg::Point>;
  std::vector<Polygon> areas_;
  std::vector<std::string> area_names_;
  
  // TF相关
  std::shared_ptr<tf2_ros::Buffer> tf_buffer_;
  std::shared_ptr<tf2_ros::TransformListener> tf_listener_;
  
  // 话题发布器
  rclcpp::Publisher<std_msgs::msg::Float32>::SharedPtr pub_scan_;
  rclcpp::Publisher<std_msgs::msg::UInt8>::SharedPtr pub_vel_;
  
  // 定时器
  rclcpp::TimerBase::SharedPtr timer_;
  
  // 定时器回调，用于检查机器人位置
  void checkRobotPosition();
  
  // 解析区域参数
  void parseAreas(const std::vector<std::string>& area_strs);
  
  // 判断点是否在多边形内（射线法）
  bool isPointInPolygon(const geometry_msgs::msg::Point& point, const Polygon& polygon);
};

#endif  // AREA_CONTROL__AREA_MONITOR_HPP_
