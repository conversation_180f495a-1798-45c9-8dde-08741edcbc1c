#!/usr/bin/env python

from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

# For integers and doubles:
#       Name                    Type      Reconfiguration level
#       Description
#       Default  Min  Max

##################################################################
###################### Foreground detection ######################
gen.add("alpha_slow", double_t, 0,
	"Foreground detection: Learning rate of the slow filter",
	0.3, 0.0, 1.0)

gen.add("alpha_fast", double_t, 0,
	"Foreground detection: Learning rate of the fast filter",
	0.85, 0.0, 1.0)

gen.add("beta", double_t, 0,
	"Foreground detection: Weighting coefficient between a pixels value and the mean of its nearest neighbors",
	0.85, 0.0, 1.0)

gen.add("min_sep_between_slow_and_fast_filter", int_t, 0,
	"Foreground detection: Minimal difference between the fast and the slow filter to recognize a obstacle as dynamic",
	80, 0, 255)

gen.add("min_occupancy_probability", int_t, 0,
	"Foreground detection: Minimal value of the fast filter to recognize a obstacle as dynamic",
	180, 0, 255)

gen.add("max_occupancy_neighbors", int_t, 0,
	"Foreground detection: Maximal mean value of the nearest neighbors of a pixel in the slow filter",
	80, 0, 255)

gen.add("morph_size", int_t, 0,
	"Foreground detection: Size of the structuring element for the closing operation",
	1, 0, 10)

gen.add("publish_static_obstacles", bool_t, 0,
        "Include static obstacles as single-point polygons",
        True)

############################################################
###################### Blob detection ######################

# These parameters are commented out, because the input image for the blob detection is already binary -> irrelevant
#gen.add("threshold_step", double_t, 0,
#	"Blob detection: Distance between neighboring thresholds",
#	256.0, 0.0, 256.0)
#
#gen.add("min_threshold", double_t, 0,
#	"Blob detection: Convert the source image to binary images by applying several thresholds, starting at min_threshold",
#	1, 0, 255)
#
#gen.add("max_threshold", double_t, 0,
#	"Blob detection: Convert the source image to binary images by applying several thresholds, ending at max_threshold",
#	255, 0, 255)
#
#gen.add("min_repeatability", int_t, 0,
#	"Blob detection: Minimal number of detections of a blob in the several thresholds to be considered as real blob",
#	1, 1, 10)
#
gen.add("min_distance_between_blobs", double_t, 0,
	"Blob detection: Minimal distance between centers of two blobs to be considered as seperate blobs",
	10, 0.0, 300.0)

gen.add("filter_by_area", bool_t, 0,
	"Blob detection: Filter blobs based on number of pixels",
	True)

gen.add("min_area", int_t, 0,
	"Blob detection: Minimal number of pixels a blob consists of",
	3, 0, 300)

gen.add("max_area", int_t, 0,
	"Blob detection: Maximal number of pixels a blob consists of",
	300, 0, 300)

gen.add("filter_by_circularity", bool_t, 0,
	"Blob detection: Filter blobs based on their circularity",
	True)

gen.add("min_circularity", double_t, 0,
	"Blob detection: Minimal circularity value (0 in case of a line)",
	0.2, 0.0, 1.0)

gen.add("max_circularity", double_t, 0,
	"Blob detection: Maximal circularity value (1 in case of a circle)",
	1.0, 0.0, 1.0)

gen.add("filter_by_inertia", bool_t, 0,
	"Blob detection: Filter blobs based on their inertia ratio",
	True)

gen.add("min_inertia_ratio", double_t, 0,
	"Blob detection: Minimal inertia ratio",
	0.2, 0.0, 1.0)

gen.add("max_inertia_ratio", double_t, 0,
	"Blob detection: Maximal inertia ratio",
	1.0, 0.0, 1.0)

gen.add("filter_by_convexity", bool_t, 0,
	"Blob detection: Filter blobs based on their convexity (Blob area / area of its convex hull)",
	False)

gen.add("min_convexity", double_t, 0,
	"Blob detection: Minimum convexity ratio",
	0.0, 0.0, 1.0)

gen.add("max_convexity", double_t, 0,
	"Blob detection: Maximal convexity ratio",
	1.0, 0.0, 1.0)

################################################################
#################### Tracking ##################################
gen.add("dt", double_t, 0,
	"Tracking: Time for one timestep of the kalman filter",
	0.2, 0.1, 3.0)

gen.add("dist_thresh", double_t, 0,
	"Tracking: Maximum distance between two points to be considered in the assignment problem",
	20.0, 0.0, 150.0)

gen.add("max_allowed_skipped_frames", int_t, 0,
	"Tracking: Maximum number of frames a object is tracked while it is not seen",
	3, 0, 10)

gen.add("max_trace_length", int_t, 0,
	"Tracking: Maximum number of Points in a objects trace",
	10, 1, 100)

exit(gen.generate("costmap_converter", "standalone_converter", "CostmapToDynamicObstacles"))
