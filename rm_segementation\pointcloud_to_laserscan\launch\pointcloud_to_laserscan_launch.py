from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node


def generate_launch_description():
    return LaunchDescription([
        DeclareLaunchArgument(
            name='scanner', default_value='scanner',
            description='Namespace for sample topics'
        ),
        Node(
            package='pointcloud_to_laserscan', executable='pointcloud_to_laserscan_node',
            remappings=[('cloud_in',  ['/segmentation/obstacle']),
                        ('scan',  ['/scan'])],
            parameters=[{
                'target_frame': 'livox_frame',
                'transform_tolerance': 0.005,  # Reduce tolerance for faster transform lookups
                'min_height': -0.4,
                'max_height': 0.35,
                'angle_min': -3.14159,
                'angle_max': 3.14159,
                'angle_increment': 0.0021,  # Increase resolution for smoother scans
                'scan_time': 0.1,  # Reduce scan time for higher frequency
                'range_min': 0.15,
                'range_max': 8.0,
                'use_inf': True,
                'inf_epsilon': 1.0,
                'theta': 45.0,
                'search_scale': 0.15,
                'alpha': 45.0,
                'voxel_size': 0.01,
                'cout': 1,
                'kdtree_radius': 0.1,
                'Tolaser_min_height': -0.3,  
                'Tolaser_max_height': 0.11   
            }],
            name='pointcloud_to_laserscan'
        )
    ])
