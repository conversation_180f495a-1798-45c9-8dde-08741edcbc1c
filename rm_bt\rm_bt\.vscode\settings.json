{"files.associations": {"iosfwd": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "any": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "*.tcc": "cpp", "bitset": "cpp", "charconv": "cpp", "chrono": "cpp", "codecvt": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "condition_variable": "cpp", "cstdint": "cpp", "deque": "cpp", "forward_list": "cpp", "list": "cpp", "map": "cpp", "set": "cpp", "string": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "future": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "numbers": "cpp", "ostream": "cpp", "ranges": "cpp", "semaphore": "cpp", "shared_mutex": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "thread": "cpp", "cinttypes": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "valarray": "cpp", "variant": "cpp", "expected": "cpp", "*.ipp": "cpp"}, "python.autoComplete.extraPaths": ["/home/<USER>/jl_rm_bt/install/rm_sentry_decision_interfaces/local/lib/python3.10/dist-packages", "/home/<USER>/jl_rm_bt/install/btcpp_ros2_interfaces/local/lib/python3.10/dist-packages", "/home/<USER>/rm/install/teb_msgs/local/lib/python3.10/dist-packages", "/home/<USER>/rm/install/rm_sentry_decision_interfaces/local/lib/python3.10/dist-packages", "/home/<USER>/rm/install/energy_interfaces/local/lib/python3.10/dist-packages", "/home/<USER>/rm/install/costmap_converter_msgs/local/lib/python3.10/dist-packages", "/home/<USER>/rm/install/auto_aim_interfaces/local/lib/python3.10/dist-packages", "/home/<USER>/ws_livox/install/fast_lio/local/lib/python3.10/dist-packages", "/home/<USER>/ws_livox/install/livox_ros_driver2/local/lib/python3.10/dist-packages", "/opt/ros/humble/lib/python3.10/site-packages", "/opt/ros/humble/local/lib/python3.10/dist-packages"], "python.analysis.extraPaths": ["/home/<USER>/jl_rm_bt/install/rm_sentry_decision_interfaces/local/lib/python3.10/dist-packages", "/home/<USER>/jl_rm_bt/install/btcpp_ros2_interfaces/local/lib/python3.10/dist-packages", "/home/<USER>/rm/install/teb_msgs/local/lib/python3.10/dist-packages", "/home/<USER>/rm/install/rm_sentry_decision_interfaces/local/lib/python3.10/dist-packages", "/home/<USER>/rm/install/energy_interfaces/local/lib/python3.10/dist-packages", "/home/<USER>/rm/install/costmap_converter_msgs/local/lib/python3.10/dist-packages", "/home/<USER>/rm/install/auto_aim_interfaces/local/lib/python3.10/dist-packages", "/home/<USER>/ws_livox/install/fast_lio/local/lib/python3.10/dist-packages", "/home/<USER>/ws_livox/install/livox_ros_driver2/local/lib/python3.10/dist-packages", "/opt/ros/humble/lib/python3.10/site-packages", "/opt/ros/humble/local/lib/python3.10/dist-packages"]}