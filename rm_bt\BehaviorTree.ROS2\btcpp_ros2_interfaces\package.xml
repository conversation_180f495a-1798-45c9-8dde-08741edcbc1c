<package format="3">
  <name>btcpp_ros2_interfaces</name>
  <version>0.2.0</version>
  <description>
  ROS2 interfaces, mostly used to demonstrate behaviortree_ros2
  </description>

  <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>
  <license>MIT</license>
  <author><PERSON><PERSON></author>


  <buildtool_depend>ament_cmake</buildtool_depend>

  <buildtool_depend>rosidl_default_generators</buildtool_depend>

  <exec_depend>rosidl_default_runtime</exec_depend>

  <depend>action_msgs</depend>

  <member_of_group>rosidl_interface_packages</member_of_group>

<export>
  <build_type>ament_cmake</build_type>
</export>

</package>
