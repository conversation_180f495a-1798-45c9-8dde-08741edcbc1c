#!/usr/bin/env python

from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

# For integers and doubles:
#       Name                    Type      Reconfiguration level
#       Description
#       Default  Min  Max


gen.add("cluster_max_distance", double_t, 0, 
	"Parameter for DB_Scan, maximum distance to neighbors [m]",
	0.4, 0.0, 10.0)

gen.add("cluster_min_pts", int_t, 0, 
	"Parameter for DB_Scan: minimum number of points that define a cluster",
	2, 1, 20)

gen.add("cluster_max_pts", int_t, 0, 
  "Parameter for DB_Scan: maximum number of points that define a cluster (limit cluster size to avoid large L- and U-shapes)",
  30, 2, 200)

gen.add("convex_hull_min_pt_separation",   double_t,   0,
	"Clear keypoints of the convex polygon that are close to each other [distance in meters] (0: keep all)",
	0.1, 0.0, 10.0)	

gen.add("concave_hull_depth",   double_t,   0,
  "Smaller depth: sharper surface, depth -> high value: convex hull",
  2.0, 0.0, 100.0) 

exit(gen.generate("costmap_converter", "standalone_converter", "CostmapToPolygonsDBSConcaveHull"))
