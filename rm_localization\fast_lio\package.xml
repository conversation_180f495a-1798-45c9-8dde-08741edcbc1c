<?xml version="1.0"?>
<package format="3">
  <name>fast_lio</name>
  <version>0.0.0</version>

  <description>
    This is a modified version of LOAM which is original algorithm
    is described in the following paper:
    <PERSON><PERSON> and <PERSON><PERSON>. LOAM: Lidar Odometry and Mapping in Real-time.
    Robotics: Science and Systems Conference (RSS). Berkeley, CA, July 2014.
  </description>

  <maintainer email="<EMAIL>">claydergc</maintainer>

  <license>BSD</license>

  <author email="<EMAIL>"><PERSON></author>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>rosidl_default_generators</buildtool_depend>
  <depend>geometry_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>rclcpp</depend>
  <depend>rospy</depend>
  <depend>std_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>common_interfaces</depend>
  <depend>tf2</depend>
  <depend>pcl_ros</depend>
  <depend>pcl_conversions</depend>
  <depend>livox_ros_driver2</depend>

  <exec_depend>rosidl_default_runtime</exec_depend>
  <member_of_group>rosidl_interface_packages</member_of_group>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>