#!/usr/bin/env python

from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

# For integers and doubles:
#       Name                    Type      Reconfiguration level
#       Description
#       Default  Min  Max


gen.add("cluster_max_distance", double_t, 0, 
	"Parameter for DB_Scan, maximum distance to neighbors [m]",
	0.4, 0.0, 10.0)

gen.add("cluster_min_pts", int_t, 0, 
	"Parameter for DB_Scan: minimum number of points that define a cluster",
	2, 1, 20)

gen.add("cluster_max_pts", int_t, 0, 
  "Parameter for DB_Scan: maximum number of points that define a cluster (limit cluster size to avoid large L- and U-shapes)",
  30, 2, 200)

gen.add("convex_hull_min_pt_separation",   double_t,   0,
	"Clear keypoints of the convex polygon that are close to each other [distance in meters] (0: keep all)",
	0.1, 0.0, 10.0)	

gen.add("support_pts_max_dist",   double_t,   0,
	"Minimum distance from a point to the line to be counted as support point",
	0.3, 0.0, 10.0)

gen.add("support_pts_max_dist_inbetween",   double_t,   0,
  "A line is only defined, if the distance between two consecutive support points is less than this treshold. Set to 0 in order to deactivate this check.",
  1.0, 0.0, 10.0)

gen.add("min_support_pts", int_t, 0, 
	"Minimum number of support points to represent a line",
	2, 0, 50)	

exit(gen.generate("costmap_converter", "standalone_converter", "CostmapToLinesDBSMCCH"))
