from launch import LaunchDescription
from launch_ros.actions import Node

def generate_launch_description():
    # 配置区域内的点（以字符串形式提供）
    areas = [
        "{1:1.45,-5.98},{2:6.08,-5.91},{3:6.08,-7.31},{4:2.78,-7.16},{5:1.07,-9.24}",  # 区域1
        "{1:17.28,3.33},{2:20.65,3.37},{3:22.29,4.98},{4:22.16,1.94},{5:17.22,2.0}"	#区域2
    ]
    
    return LaunchDescription([
        Node(
            package="polygon_publisher",
            executable="polygon_publisher",
            name="polygon_publisher_node",
            parameters=[
                {"areas": areas}  # 将区域点作为参数传递
            ]
        )
    ])
