controller_server:
  ros__parameters:
    odom_topic: /odom
    use_sim_time: False
    controller_frequency: 5.0
    controller_plugin_types: ["teb_local_planner::TebLocalPlannerROS"]
    controller_plugins: ["FollowPath"]
    FollowPath:
      plugin: teb_local_planner::TebLocalPlannerROS
      teb_autosize: 1.0
      dt_ref: 0.4
      dt_hysteresis: 0.04
      max_samples: 500
      global_plan_overwrite_orientation: True
      allow_init_with_backwards_motion: False
      max_global_plan_lookahead_dist: 7.0
      global_plan_viapoint_sep: 0.5
      global_plan_prune_distance: 1.0
      exact_arc_length: False
      feasibility_check_no_poses: 0
      publish_feedback: False
          
      # Robot
              
      max_vel_x: 1.0
      max_vel_x_backwards: 1.0
      max_vel_y: 1.0
      max_vel_theta: 0.8
      acc_lim_x: 1.25
      acc_lim_y: 1.25
      acc_lim_theta: 1.0
      min_turning_radius: 0.0 # omni-drive robot (can turn on place!)
      footprint_model:
        type: "point"
      # GoalTolerance
          
      xy_goal_tolerance: 0.0
      yaw_goal_tolerance: 3.14
      free_goal_vel: False
      complete_global_plan: True
          
      # Obstacles
          
      min_obstacle_dist: 0.4 # This value must also include our robot radius, since footprint_model is set to "point".
      inflation_dist: 0.5
      include_costmap_obstacles: True
      costmap_obstacles_behind_robot_dist: 1.0
      obstacle_poses_affected: 15
      costmap_converter_plugin: ""
      costmap_converter_spin_thread: True
      costmap_converter_rate: 5
      # Optimization
          
      no_inner_iterations: 5
      no_outer_iterations: 4
      optimization_activate: True
      optimization_verbose: False
      penalty_epsilon: 0.15
      obstacle_cost_exponent: 4.0
      weight_max_vel_x: 2.0
      weight_max_vel_y: 2.0
      weight_max_vel_theta: 1.0
      weight_acc_lim_x: 1.0
      weight_acc_lim_y: 1.0
      weight_acc_lim_theta: 1.0
      weight_kinematics_nh: 1.0 # WE HAVE A HOLONOMIC ROBOT, JUST ADD A SMALL PENALTY
      weight_kinematics_forward_drive: 1000.0
      weight_kinematics_turning_radius: 1.0
      weight_optimaltime: 1.0 # must be > 0
      weight_shortest_path: 0.0
      weight_obstacle: 100.0
      weight_inflation: 0.2
      weight_dynamic_obstacle: 10.0
      weight_dynamic_obstacle_inflation: 0.2
      weight_viapoint: 1.0
      weight_adapt_factor: 2.0
      # Homotopy Class Planner
      enable_homotopy_class_planning: True
      enable_multithreading: True
      max_number_classes: 4
      selection_cost_hysteresis: 1.0
      selection_prefer_initial_plan: 0.9
      selection_obst_cost_scale: 1.0
      selection_alternative_time_cost: False
      
      roadmap_graph_no_samples: 15
      roadmap_graph_area_width: 5.0
      roadmap_graph_area_length_scale: 1.0
      h_signature_prescaler: 0.5
      h_signature_threshold: 0.1
      obstacle_heading_threshold: 0.45
      switching_blocking_period: 0.0
      viapoints_all_candidates: True
      delete_detours_backwards: True
      max_ratio_detours_duration_best_duration: 3.0
      visualize_hc_graph: False
      visualize_with_time_as_z_axis_scale: 0.0
      # Recovery
      
      #shrink_horizon_backup: True
      #shrink_horizon_min_duration: 10.0
      #oscillation_recovery: True
      #oscillation_v_eps: 0.1
      #oscillation_omega_eps: 0.1
      #oscillation_recovery_min_duration: 10.0
      #oscillation_filter_duration: 10.0
