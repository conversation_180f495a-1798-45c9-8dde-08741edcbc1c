{"C_Cpp_Runner.cCompilerPath": "gcc", "C_Cpp_Runner.cppCompilerPath": "g++", "C_Cpp_Runner.debuggerPath": "gdb", "C_Cpp_Runner.cStandard": "", "C_Cpp_Runner.cppStandard": "", "C_Cpp_Runner.msvcBatchPath": "C:/Program Files/Microsoft Visual Studio/VR_NR/Community/VC/Auxiliary/Build/vcvarsall.bat", "C_Cpp_Runner.useMsvc": false, "C_Cpp_Runner.warnings": ["-Wall", "-Wextra", "-Wpedantic", "-W<PERSON>dow", "-Wformat=2", "-Wcast-align", "-Wconversion", "-Wsign-conversion", "-W<PERSON><PERSON>-dereference"], "C_Cpp_Runner.msvcWarnings": ["/W4", "/permissive-", "/w14242", "/w14287", "/w14296", "/w14311", "/w14826", "/w44062", "/w44242", "/w14905", "/w14906", "/w14263", "/w44265", "/w14928"], "C_Cpp_Runner.enableWarnings": true, "C_Cpp_Runner.warningsAsError": false, "C_Cpp_Runner.compilerArgs": [], "C_Cpp_Runner.linkerArgs": [], "C_Cpp_Runner.includePaths": [], "C_Cpp_Runner.includeSearch": ["*", "**/*"], "C_Cpp_Runner.excludeSearch": ["**/build", "**/build/**", "**/.*", "**/.*/**", "**/.vscode", "**/.vscode/**"], "C_Cpp_Runner.useAddressSanitizer": false, "C_Cpp_Runner.useUndefinedSanitizer": false, "C_Cpp_Runner.useLeakSanitizer": false, "C_Cpp_Runner.showCompilationTime": false, "C_Cpp_Runner.useLinkTimeOptimization": false, "C_Cpp_Runner.msvcSecureNoWarnings": false, "python.autoComplete.extraPaths": ["/home/<USER>/NUDT_navigation/install/vikit_py/lib/python3.10/site-packages", "/home/<USER>/NUDT_navigation/install/teb_msgs/local/lib/python3.10/dist-packages", "/home/<USER>/NUDT_navigation/install/faster_lio/local/lib/python3.10/dist-packages", "/home/<USER>/NUDT_navigation/install/fast_lio/local/lib/python3.10/dist-packages", "/home/<USER>/NUDT_navigation/install/livox_ros_driver2/local/lib/python3.10/dist-packages", "/home/<USER>/NUDT_navigation/install/costmap_converter_msgs/local/lib/python3.10/dist-packages", "/home/<USER>/NUDT_bt/install/rm_sentry_decision_interfaces/local/lib/python3.10/dist-packages", "/home/<USER>/NUDT_bt/install/btcpp_ros2_interfaces/local/lib/python3.10/dist-packages", "/opt/ros/humble/lib/python3.10/site-packages", "/opt/ros/humble/local/lib/python3.10/dist-packages"], "python.analysis.extraPaths": ["/home/<USER>/NUDT_navigation/install/vikit_py/lib/python3.10/site-packages", "/home/<USER>/NUDT_navigation/install/teb_msgs/local/lib/python3.10/dist-packages", "/home/<USER>/NUDT_navigation/install/faster_lio/local/lib/python3.10/dist-packages", "/home/<USER>/NUDT_navigation/install/fast_lio/local/lib/python3.10/dist-packages", "/home/<USER>/NUDT_navigation/install/livox_ros_driver2/local/lib/python3.10/dist-packages", "/home/<USER>/NUDT_navigation/install/costmap_converter_msgs/local/lib/python3.10/dist-packages", "/home/<USER>/NUDT_bt/install/rm_sentry_decision_interfaces/local/lib/python3.10/dist-packages", "/home/<USER>/NUDT_bt/install/btcpp_ros2_interfaces/local/lib/python3.10/dist-packages", "/opt/ros/humble/lib/python3.10/site-packages", "/opt/ros/humble/local/lib/python3.10/dist-packages"], "cmake.sourceDirectory": "/home/<USER>/NUDT_navigation/src/rm_driver/Livox-SDK2", "files.associations": {"cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "bitset": "cpp", "chrono": "cpp", "cinttypes": "cpp", "codecvt": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "condition_variable": "cpp", "cstdint": "cpp", "deque": "cpp", "forward_list": "cpp", "list": "cpp", "map": "cpp", "set": "cpp", "string": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "regex": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "future": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "numbers": "cpp", "ostream": "cpp", "semaphore": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "thread": "cpp", "cfenv": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "variant": "cpp"}}