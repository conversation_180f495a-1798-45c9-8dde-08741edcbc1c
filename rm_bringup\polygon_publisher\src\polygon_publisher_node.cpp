#include "rclcpp/rclcpp.hpp"
#include "visualization_msgs/msg/marker.hpp"
#include "visualization_msgs/msg/marker_array.hpp"
#include "geometry_msgs/msg/point.hpp"
#include "rclcpp/parameter.hpp"
#include <map>

class AreaVisualizer : public rclcpp::Node {
public:
    AreaVisualizer() : Node("area_visualizer") {
        // 创建发布者，话题类型为MarkerArray
        publisher_ = this->create_publisher<visualization_msgs::msg::MarkerArray>(
            "/map_areas", 10);

        // 从参数加载区域点
        this->declare_parameter<std::vector<std::string>>("areas", {});
        auto areas_param = this->get_parameter("areas").as_string_array();

        // 解析区域点
        for (const auto& area_str : areas_param) {
            std::vector<geometry_msgs::msg::Point> points;
            std::istringstream iss(area_str);
            std::string point_str;

            // 按 '}' 分隔每个点
            while (std::getline(iss, point_str, '}')) {
                size_t start = point_str.find('{');
                if (start != std::string::npos) {
                    point_str = point_str.substr(start + 1); // 去掉 '{'
                    double x, y;
                    if (sscanf(point_str.c_str(), "%*d:%lf,%lf", &x, &y) == 2) {
                        points.push_back(create_point(x, y));
                    }
                }
            }

            if (points.size() >= 3) {
                areas_.push_back(points);
            } else {
                RCLCPP_WARN(this->get_logger(), "区域点数量不足3个，忽略该区域");
            }
        }

        // 定义定时器，每100毫秒发布一次数据
        timer_ = this->create_wall_timer(
            std::chrono::milliseconds(100),
            std::bind(&AreaVisualizer::publish_areas, this));
    }

private:
    void publish_areas() {
        visualization_msgs::msg::MarkerArray marker_array;

        // 为每个区域生成Marker
        for (size_t i = 0; i < areas_.size(); ++i) {
            auto marker = create_area_marker(i, areas_[i]);
            marker_array.markers.push_back(marker);
        }

        publisher_->publish(marker_array);
    }

    // 辅助函数：创建Point消息
    geometry_msgs::msg::Point create_point(double x, double y) {
        geometry_msgs::msg::Point p;
        p.x = x;
        p.y = y;
        p.z = 0.0;
        return p;
    }

    // 辅助函数：生成单个区域Marker
    visualization_msgs::msg::Marker create_area_marker(
        int id, const std::vector<geometry_msgs::msg::Point>& points) 
    {
        visualization_msgs::msg::Marker marker;
        marker.header.frame_id = "map";  // 坐标系设为map
        marker.header.stamp = this->now();
        marker.ns = "map_areas";         // 命名空间统一
        marker.id = id;                  // 唯一ID区分不同区域
        marker.type = visualization_msgs::msg::Marker::LINE_STRIP;
        marker.action = visualization_msgs::msg::Marker::ADD;
        
        // 设置颜色和尺寸
        marker.color.r = (id % 3 == 0) ? 1.0 : 0.0;  // 红色
        marker.color.g = (id % 3 == 1) ? 1.0 : 0.0;  // 绿色
        marker.color.b = (id % 3 == 2) ? 1.0 : 0.0;  // 蓝色
        marker.color.a = 0.8;                        // 透明度
        marker.scale.x = 0.1;                        // 线宽
        
        // 添加顶点
        marker.points = points;

        // 确保区域是封闭的
        if (!points.empty() && 
            (points.front().x != points.back().x || points.front().y != points.back().y)) {
            marker.points.push_back(points.front());
        }

        return marker;
    }

    rclcpp::Publisher<visualization_msgs::msg::MarkerArray>::SharedPtr publisher_;
    rclcpp::TimerBase::SharedPtr timer_;
    std::vector<std::vector<geometry_msgs::msg::Point>> areas_;
};

int main(int argc, char** argv) {
    rclcpp::init(argc, argv);
    rclcpp::spin(std::make_shared<AreaVisualizer>());
    rclcpp::shutdown();
    return 0;
}
