# JunLin_navigation2025

#### 介绍
RoboMaster20205赛季军临战队导航算法仓库

首先致谢中南大学FYT战队、深圳北理莫斯科大学北极熊战队、湖南大学跃鹿战队的开源与交流，本赛季军临战队采用3d雷达mid360与2d雷达融合导航避障，行为树进行自主决策的方案。
#### 
### 环境
- 系统:Ubuntu22.04
- ros2:humble

####


#### 软件架构
软件架构说明
####
1. rm_driver
- livox_ros_driver2

    用于mid-360的激光雷达驱动，输出/livox/imu、/livox/lidar话题信息

- oradar_ros

    用于Oradar MS200p 2D d雷达驱动，已经进行过相关修改，可通过类型为std_msgs::msg::Float32的话题/if_pub_ms200_scan控制是否发布有效的Laserscan类型的点云信息；当/if_pub_ms200_scan数据为                1.0时，发布有效的Laserscan类型的点云信息，数据为0.0发布空的Laserscan类型点云信息。

2. rm_localiztion
- fast_lio

    ros2 版本fast_lio，由香港大学团队开发的高效激光雷达-惯性里程计算法，其核心采用紧耦合迭代扩展卡尔曼滤波器（iEKF）​，融合激光雷达（LiDAR）特征点与惯性测量单元（IMU）数据，实现高精度位姿估计。
- faster_lio

    ros2 版本 faster_lio，由香港大学团队进一步优化，为Fast-LIO2的升级版本，目前配置了mid-360激光雷达相关启动文件。但是目前仅用于正装雷达，倾斜一定角度安装还没有进行配置，有需求的朋友可以进行私下配置。

- point_lio

    香港大学团队提出的直接点云配准激光雷达-惯性里程计算法，摒弃传统特征提取方式，直接利用原始点云与IMU数据进行紧耦合优化。

三者比较，个人认为fast_lio鲁棒性差，point_lio 内存占用率大，总体来说faster_lio不论是鲁棒性还是内存占用率都更胜一筹。

3. rm_segementation
- linefit_ground_segementation_ros2
    
    为点云分割算法，将输入的空间点云分割为障碍物和地面，是一种基于局部线模型拟合的激光雷达（LiDAR）地面分割算法。其核心将点云划分为局部区域，通过最小二乘法拟合直线，结合高度差与角度阈值判断地面点，避免传统网格法在斜坡/起伏地形的失效。优势包括低计算量（实时处理）​、动态阈值适应复杂场景，以及高鲁棒性（抗噪点干扰）。

- pointcloud_to_laserscan
    该版本非传统pointcloud_to_laserscan，添加了斜坡处理的功能。使用kd-tree、降采样等功能，将一定角度的斜坡点云进行删除。
    
    新添加的参数
    
    'theta': 45.0

    表示斜坡最大角度，当角度小于这个设定值时，会对斜坡点云进行删除

    'search_scale': 0.15
    
    表示斜坡点云处理的精度，理论上越小越好，但是受制于输入点云在空间中是离散的，并且每个点云之间存在一定间隔，所以这个值不能设置过大或者过小

    'alpha': 45.0

    α角度默认值为45，修改对结果理论上影响不大，还是受制于输入点云在空间中离散性，这个值设置效果合理即可

    'voxel_size': 0.01
    
    降采样尺寸大小

    'cout': 1

    如果输出Laserscan类型的/scan信息过于稀疏，可以通过发布当前与上(cout-1)融合在一起后的信息。提升点云的紧密程度，但是cout值太大会影响避障的快速性与准确性。默认为1，发布当前帧Laserscan类型的/scan即可。

    'kdtree_radius': 0.1

    为了提升斜坡处理的速度，引入了kd-tree算法，表示搜索当前需要进行判断的点云，局部搜索与判断范围。理论越大越好，但是注意对性能的影响。

    'Tolaser_min_height': -0.3 

    'Tolaser_max_height': 0.11

    最后处理好的点云，压缩一激光雷达坐标系为原点，高度范围[Tolaser_min_height,Tolaser_max_height]内的点云压缩为Laserscan类型的点云话题/scan

4. rm_rm_bringup
- area_control
    
    实时监测机器人坐标系(base_link)相对map坐标系下的位置，判断当前机器人是否在某些区域（如斜坡区域）里，控制2d雷达融合与上坡减速。本赛季采用2d雷达融合的方案，但是对于一些如斜坡的路段,2d雷达会把斜坡误认为是障碍物，并且在上坡时，全向轮机器人速度太快容易出现撞墙或者因为速度太快偏离原本规划路线的情况。因此，本人对斜坡区域做了相关处理，当检测到机器人在斜坡区域内时，控制类型为std_msgs::msg::Float32、话题名为/if_pub_ms200_scan的话题发布器发布数据0.0，2雷达驱动话题订阅器订阅到0.0的数据时，发布空的点云信息（/MS200/scan），效果等效于取消2d雷达融合，只使用mid-360激光雷达处理后的/scan；检测到机器人在某些范围内的同时，会控制消息类型为std_msgs::msg::UInt8，话题名为/vel_control的话题发布器发布数据0，通过串口通信发布给电控端进行降速处理。

    在启动文件area_monitor.launch.py中配置区域参数
![输入图片说明](rm_bringup/area_control/%E5%8C%BA%E5%9F%9F%E5%8C%B9%E9%85%8D%E5%80%BC.png)

|话题名|消息类型|机器人处于斜坡范围内数据|机器人不处于斜坡范围内数据|
|---|---|---|---|
|/if_pub_ms200_scan|std_msgs::msg::Float32|0.0|1.0|
|/vel_control|std_msgs::msg::UInt8|0|1|

- polygon_publisher

    配合area_control使用，在启动文件polygon_publisher_launch.py中添加区域，导航时rviz2中可以订阅其输出的话题信息，可视化区域范围（如斜坡）

- location

    为查看坐标点的小工具，将栅格地图以及其对应的配置文件放入map文件夹下，在启动文件map_visualizer_launch.py下修改地图参数文件（如RMUC2025.yaml）后，启动启动文件即可。

    点击地图可以显示对应的坐标，c键可以清除所标记的坐标点
![输入图片说明](rm_bringup/location/%E5%90%AF%E5%8A%A8%E7%A4%BA%E4%BE%8B.png)

- jl_nav2_bringup
    Navigation2导航堆栈的核心启动与配置工具​，其中局部路径规划器采用teb，在nav2_params_teb.yaml中添加了一个传感器输入(2d雷达融合)
    
    关于2d雷达融合只需要做到如下两个步骤

    发布静态tf坐标变换，即2d雷达相对机器人坐标系的位置
    ![输入图片说明](2d%E9%9B%B7%E8%BE%BE%E9%9D%99%E6%80%81tf.png)

    在nav2配置文件中添加一个障碍物层，可以按照/scan的示例来添加
    ![输入图片说明](rm_bringup/jl_nav2_bringup/%E7%A4%BA%E4%BE%8B.png)

    由于2d雷达畸变这里并没有进行处理，其畸变在机器人旋转角速度不变的情况下与距离成正比，因此这里在nav2_params_teb.yaml配置文件中仅使用[0.1,1.5]m范围内的2d点云信息进行避障，畸变并不明显。

5. rm_slam
    slamtoolbox建图，这里建图使用的是offline模式，启动启动文件offline_launch.py即可，config下关键参数如下
    
    odom_frame: odom

    map_frame: map

    base_frame: livox_frame

    scan_topic: /scan

    livox_frame表示激光雷达坐标系，/scan为经过处理后的Laserscan信息，用于建图

    如果进行建图，这里更推荐使用pcd转栅格地图，而不使用slamtoolbox，得到的栅格地图更好的匹配物理世界特征

6. rm_bt 
    - BehaviorTree.ROS2

    行为树相关依赖，修改了bt_action_node.hpp基类，在halt()中解决了因为行为树条件发生改变时action 报的Goalhandle error错误，即调用whledoelse判断条件发生改变时行为树程序死亡问题。解决
思路为条件发生改变，调用halt()时直接强制对任务节点赋值Success。

    - rm_bt

    行为树核心，包括编写的各种行为树插件以及决策代码

    - rm_sentry_decision_interfaces
    
    行为树需要的自定义消息类型

7. rm_relocalization
    - icp_registration

    只重定位一次，即初重定位，要求有良好的启动位姿。icp.yaml文件中配置initial_pose，先验pcd地图。这里initial_pose配置为建图启动点，要求后续使用icp时给予一个良好的配准点，基本与建图起点一致，包括位置与位姿。配准成功后持续发布高频率 map 到 odom坐标系 的动态坐标变换。如果使用这个包，建议配合定位精度较高、鲁棒性更好的faster_lio进行使用。

    - small_gicp_relocalization 

    [深北莫北极熊战队重定位](https://github.com/SMBU-PolarBear-Robotics-Team/small_gicp_relocalization/tree/915e120d481544184412c46d4b7173764f85072d)

    这里map to odom 的坐标变换频率很低，timer定时器个人觉得有点问题，湖大跃鹿战队因此自己写了一套实时重定位，这里更建议使用湖大的重定位，效果更佳

    [湖大跃鹿战队2025年导航算法仓库](https://gitee.com/hnuyuelurm/hnunavigation_-ros2)

### 标题

#### 安装教程

1.  xxxx
2.  xxxx
3.  xxxx

#### 使用说明

1.  xxxx
2.  xxxx
3.  xxxx

#### 参与贡献

1.  Fork 本仓库
2.  新建 Feat_xxx 分支
3.  提交代码
4.  新建 Pull Request


#### 特技

1.  使用 Readme\_XXX.md 来支持不同的语言，例如 Readme\_en.md, Readme\_zh.md
2.  Gitee 官方博客 [blog.gitee.com](https://blog.gitee.com)
3.  你可以 [https://gitee.com/explore](https://gitee.com/explore) 这个地址来了解 Gitee 上的优秀开源项目
4.  [GVP](https://gitee.com/gvp) 全称是 Gitee 最有价值开源项目，是综合评定出的优秀开源项目
5.  Gitee 官方提供的使用手册 [https://gitee.com/help](https://gitee.com/help)
6.  Gitee 封面人物是一档用来展示 Gitee 会员风采的栏目 [https://gitee.com/gitee-stars/](https://gitee.com/gitee-stars/)
