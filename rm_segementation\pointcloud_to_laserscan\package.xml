<?xml version="1.0"?>
<package format="2">
    <name>pointcloud_to_laserscan</name>
    <version>2.0.1</version>
    <description>Converts a 3D Point Cloud into a 2D laser scan. This is useful for making devices like the Kinect appear like a laser scanner for 2D-based algorithms (e.g. laser-based SLAM).</description>

    <maintainer email="<EMAIL>"><PERSON></maintainer>
    <maintainer email="<EMAIL>"><PERSON></maintainer>
    <author email="<EMAIL>"><PERSON></author>
    <author email="<EMAIL>"><PERSON></author>
    <author><PERSON><PERSON></author>

    <license>BSD</license>

    <url type="website">http://ros.org/wiki/perception_pcl</url>
    <url type="bugtracker">https://github.com/ros-perception/perception_pcl/issues</url>
    <url type="repository">https://github.com/ros-perception/perception_pcl</url>

    <buildtool_depend>ament_cmake</buildtool_depend>

    <depend>laser_geometry</depend>
    <depend>message_filters</depend>
    <depend>rclcpp</depend>
    <depend>rclcpp_components</depend>
    <depend>sensor_msgs</depend>
    <depend>tf2</depend>
    <depend>tf2_ros</depend>
    <depend>tf2_sensor_msgs</depend>
    <depend>pcl_conversions</depend>
    <depend>pcl_ros</depend>

    <exec_depend>launch</exec_depend>
    <exec_depend>launch_ros</exec_depend>

    <test_depend>ament_lint_auto</test_depend>
    <!-- <exec_depend>ament_cmake_copyright</exec_depend> -->
    <test_depend>ament_cmake_cppcheck</test_depend>
    <test_depend>ament_cmake_cpplint</test_depend>
    <test_depend>ament_cmake_flake8</test_depend>
    <test_depend>ament_cmake_lint_cmake</test_depend>
    <test_depend>ament_cmake_pep257</test_depend>
    <test_depend>ament_cmake_uncrustify</test_depend>
    <test_depend>ament_cmake_xmllint</test_depend>

    <export>
        <build_type>ament_cmake</build_type>
    </export>
</package>
