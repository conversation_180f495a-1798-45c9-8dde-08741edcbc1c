{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": false, "cwd": "/home/<USER>/NUDT_navigation/src/rm_relocalization/small_gicp_relocalization/src", "program": "/home/<USER>/NUDT_navigation/src/rm_relocalization/small_gicp_relocalization/src/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}